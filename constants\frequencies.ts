// Healing frequency configurations
export const FREQUENCIES = {
  normal: [
    { freq: 174, label: "174 Hz - Pain Relief", duration: 30 },
    { freq: 285, label: "285 Hz - Tissue Healing", duration: 30 },
    { freq: 396, label: "396 Hz - Liberation", duration: 30 },
    { freq: 417, label: "417 Hz - Change", duration: 30 },
    { freq: 528, label: "528 Hz - Love", duration: 30 },
    { freq: 639, label: "639 Hz - Connection", duration: 30 },
    { freq: 741, label: "741 Hz - Expression", duration: 30 },
    { freq: 852, label: "852 Hz - Intuition", duration: 30 },
    { freq: 963, label: "963 Hz - Unity", duration: 30 },
  ],
  pleasure: [
    { freq: 40, label: "40 Hz - Focus", duration: 45 },
    { freq: 110, label: "110 Hz - Beta Endorphins", duration: 45 },
    { freq: 220, label: "220 Hz - Dopamine", duration: 45 },
    { freq: 440, label: "440 Hz - Serotonin", duration: 45 },
    { freq: 880, label: "880 Hz - Oxytocin", duration: 45 },
  ],
  antiPain: [
    { freq: 95, label: "95 Hz - Pain Gate", duration: 60 },
    { freq: 125, label: "125 Hz - Nerve Block", duration: 60 },
    { freq: 200, label: "200 Hz - Inflammation", duration: 60 },
    { freq: 304, label: "304 Hz - Muscle Relief", duration: 60 },
    { freq: 500, label: "500 Hz - Endorphins", duration: 60 },
  ],
}

// Piano chord progressions for ambient accompaniment
export const CHORD_PROGRESSIONS = {
  gentleWorship: {
    bpm: 60,
    chords: [
      { notes: ["C4", "E4", "G4"], duration: 4, pause: false },
      { notes: ["F4", "A4", "C5"], duration: 4, pause: false },
      { notes: ["G4", "B4", "D5"], duration: 4, pause: false },
      { notes: ["C4", "E4", "G4"], duration: 4, pause: false },
      { notes: [], duration: 2, pause: true },
    ],
  },
  ephemeralTime: {
    bpm: 45,
    chords: [
      { notes: ["A3", "C4", "E4"], duration: 6, pause: false },
      { notes: ["F3", "A3", "C4"], duration: 6, pause: false },
      { notes: ["G3", "B3", "D4"], duration: 6, pause: false },
      { notes: [], duration: 4, pause: true },
    ],
  },
  riverFlow: {
    bpm: 50,
    chords: [
      { notes: ["D4", "F#4", "A4"], duration: 5, pause: false },
      { notes: ["G3", "B3", "D4"], duration: 5, pause: false },
      { notes: ["A3", "C#4", "E4"], duration: 5, pause: false },
      { notes: ["D4", "F#4", "A4"], duration: 5, pause: false },
      { notes: [], duration: 3, pause: true },
    ],
  },
  celestialDance: {
    bpm: 55,
    chords: [
      { notes: ["E4", "G#4", "B4"], duration: 4, pause: false },
      { notes: ["A3", "C#4", "E4"], duration: 4, pause: false },
      { notes: ["B3", "D#4", "F#4"], duration: 4, pause: false },
      { notes: ["E4", "G#4", "B4"], duration: 4, pause: false },
      { notes: [], duration: 2, pause: true },
    ],
  },
}
