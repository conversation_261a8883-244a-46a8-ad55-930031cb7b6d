"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { Minus, Plus, Maximize2, Minimize2, Move } from "lucide-react"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, BrownianLayer, AudioLevels } from "../../constants/types"

// dB Level Meter Component
function DbMeter({ label, level, color = "cyan" }: { label: string; level: number; color?: string }) {
  // Convert dB to percentage for visual display (-60dB = 0%, 0dB = 100%)
  const percentage = Math.max(0, Math.min(100, ((level + 60) / 60) * 100))

  const getColorClass = () => {
    if (level > -6) return "bg-red-500" // Danger zone
    if (level > -12) return "bg-yellow-500" // Warning zone
    if (level > -24) return "bg-green-500" // Good zone
    return "bg-blue-500" // Quiet zone
  }

  const getTextColor = () => {
    if (level > -6) return "text-red-400"
    if (level > -12) return "text-yellow-400"
    if (level > -24) return "text-green-400"
    return "text-blue-400"
  }

  return (
    <div className="flex items-center space-x-2 mb-2">
      <span className="text-xs text-gray-300 w-16">{label}:</span>
      <div className="flex-1 bg-gray-700 rounded-full h-3 relative overflow-hidden">
        <div className={`h-full transition-all duration-300 ${getColorClass()}`} style={{ width: `${percentage}%` }} />
        {/* dB markers */}
        <div className="absolute inset-0 flex justify-between items-center px-1">
          <span className="text-xs text-white/50">-60</span>
          <span className="text-xs text-white/50">-30</span>
          <span className="text-xs text-white/50">0</span>
        </div>
      </div>
      <span className={`text-xs w-12 text-right ${getTextColor()}`}>{level.toFixed(1)}dB</span>
    </div>
  )
}

interface SpatialVisualizationProps {
  isVisible: boolean
  onToggle: () => void
  layers: SpatialLayer[]
  brownianLayer: BrownianLayer
  onUpdateLayer: (layerId: number, updates: Partial<SpatialLayer>) => void
  onUpdateBrownianLayer: (updates: Partial<BrownianLayer>) => void
  activePositions: Set<string>
  audioLevels: AudioLevels
}

export function SpatialVisualization({
  isVisible,
  onToggle,
  layers,
  brownianLayer,
  onUpdateLayer,
  onUpdateBrownianLayer,
  activePositions,
  audioLevels,
}: SpatialVisualizationProps) {
  const [isMinimized, setIsMinimized] = useState(false)
  const [isDragging, setIsDragging] = useState(false)
  const [position, setPosition] = useState({ x: 20, y: 20 })
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true)
    setDragStart({
      x: e.clientX - position.x,
      y: e.clientY - position.y,
    })
  }

  const handleMouseMove = (e: MouseEvent) => {
    if (isDragging) {
      setPosition({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y,
      })
    }
  }

  const handleMouseUp = () => {
    setIsDragging(false)
  }

  useEffect(() => {
    if (isDragging) {
      document.addEventListener("mousemove", handleMouseMove)
      document.addEventListener("mouseup", handleMouseUp)
      return () => {
        document.removeEventListener("mousemove", handleMouseMove)
        document.removeEventListener("mouseup", handleMouseUp)
      }
    }
  }, [isDragging, dragStart])

  // Calculate total volume to ensure it equals 100%
  const totalVolume = layers.reduce((sum, layer) => sum + layer.volume, 0) + brownianLayer.volume
  const volumeWarning = Math.abs(totalVolume - 100) > 0.1

  if (!isVisible) return null

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.8 }}
      className="fixed bg-black/90 backdrop-blur-sm border border-cyan-400/30 rounded-lg shadow-2xl z-50"
      style={{
        left: position.x,
        top: position.y,
        width: isMinimized ? "200px" : "450px",
        height: isMinimized ? "40px" : "700px",
      }}
    >
      {/* Header */}
      <div
        className="flex items-center justify-between p-3 border-b border-cyan-400/20 cursor-move"
        onMouseDown={handleMouseDown}
      >
        <div className="flex items-center space-x-2">
          <Move size={16} className="text-cyan-400" />
          <span className="text-cyan-100 text-sm font-medium">3D Audio Control</span>
        </div>
        <div className="flex items-center space-x-1">
          <button onClick={() => setIsMinimized(!isMinimized)} className="p-1 hover:bg-cyan-400/20 rounded">
            {isMinimized ? (
              <Maximize2 size={14} className="text-cyan-400" />
            ) : (
              <Minimize2 size={14} className="text-cyan-400" />
            )}
          </button>
          <button onClick={onToggle} className="p-1 hover:bg-red-400/20 rounded text-red-400">
            ×
          </button>
        </div>
      </div>

      {/* Content */}
      {!isMinimized && (
        <div className="flex flex-col h-full">
          {/* dB Level Meters */}
          <div className="bg-gray-900/50 rounded-lg p-4 m-4 mb-2 flex-shrink-0">
            <div className="text-xs text-cyan-300 mb-3">Audio Level Monitor</div>
            <DbMeter label="Layer 1" level={audioLevels.layer1} />
            <DbMeter label="Layer 2" level={audioLevels.layer2} />
            <DbMeter label="Layer 3" level={audioLevels.layer3} />
            <DbMeter label="Brownian" level={audioLevels.brownian} />
            <div className="border-t border-gray-600 pt-2 mt-2">
              <DbMeter label="Master" level={audioLevels.master} />
            </div>
          </div>

          {/* Volume Warning */}
          {volumeWarning && (
            <div className="bg-red-900/50 border border-red-500/50 rounded-lg p-2 m-4 mb-2">
              <div className="text-red-300 text-xs text-center">
                Total Volume: {totalVolume.toFixed(1)}% (Should be 100%)
              </div>
            </div>
          )}

          {/* Top-down view - fixed height */}
          <div className="relative bg-gray-900/50 rounded-lg p-4 h-48 flex-shrink-0 mx-4">
            <div className="text-xs text-cyan-300 mb-2">Top-Down View</div>
            <svg viewBox="-100 -100 200 200" className="w-full h-full">
              {/* Center point (user) */}
              <circle cx="0" cy="0" r="3" fill="#ffffff" />
              <text x="0" y="-8" textAnchor="middle" className="text-xs fill-white">
                You
              </text>

              {/* Draw layers */}
              {layers.map((layer, layerIndex) => (
                <g key={layer.id}>
                  {/* Layer circle */}
                  <circle
                    cx="0"
                    cy="0"
                    r={layer.distance * 20}
                    fill="none"
                    stroke="#00ffff"
                    strokeWidth="0.5"
                    opacity="0.3"
                  />

                  {/* Audio positions */}
                  {layer.positions.map((pos, posIndex) => {
                    const x = Math.cos((pos.angle * Math.PI) / 180) * layer.distance * 20
                    const y = Math.sin((pos.angle * Math.PI) / 180) * layer.distance * 20
                    const isActive = activePositions.has(`${layer.id}-${posIndex}`)

                    return (
                      <circle
                        key={posIndex}
                        cx={x}
                        cy={y}
                        r={isActive ? "4" : "2"}
                        fill={isActive ? "#00ff00" : "#00ffff"}
                        className={isActive ? "animate-pulse" : ""}
                      />
                    )
                  })}

                  {/* Layer label */}
                  <text x={layer.distance * 20 + 5} y="0" className="text-xs fill-cyan-300">
                    L{layerIndex + 1}
                  </text>
                </g>
              ))}

              {/* Brownian noise indicator (center) */}
              {brownianLayer.isActive && (
                <circle
                  cx="0"
                  cy="0"
                  r="6"
                  fill="none"
                  stroke="#ffa500"
                  strokeWidth="2"
                  opacity="0.7"
                  className="animate-pulse"
                />
              )}
            </svg>
          </div>

          {/* Scrollable layer controls */}
          <div className="flex-1 overflow-y-auto p-4 space-y-3 min-h-0">
            {/* Spatial Layers */}
            {layers.map((layer, index) => (
              <div key={layer.id} className="bg-gray-800/50 rounded-lg p-3 flex-shrink-0">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-cyan-100 text-sm font-medium">Layer {index + 1}</span>
                  <span className="text-xs text-gray-400">{layer.volume}% volume</span>
                </div>

                {/* Distance control */}
                <div className="flex items-center space-x-2 mb-2">
                  <span className="text-xs text-gray-300 w-16">Distance:</span>
                  <button
                    onClick={() =>
                      onUpdateLayer(layer.id, {
                        distance: Math.max(0.5, layer.distance - 0.25),
                      })
                    }
                    className="p-1 bg-cyan-600/20 hover:bg-cyan-600/40 rounded"
                  >
                    <Minus size={12} className="text-cyan-400" />
                  </button>
                  <span className="text-xs text-white w-12 text-center">{(layer.distance * 4).toFixed(1)}"</span>
                  <button
                    onClick={() =>
                      onUpdateLayer(layer.id, {
                        distance: layer.distance + 0.25,
                      })
                    }
                    className="p-1 bg-cyan-600/20 hover:bg-cyan-600/40 rounded"
                  >
                    <Plus size={12} className="text-cyan-400" />
                  </button>
                </div>

                {/* Volume control */}
                <div className="flex items-center space-x-2">
                  <span className="text-xs text-gray-300 w-16">Volume:</span>
                  <button
                    onClick={() =>
                      onUpdateLayer(layer.id, {
                        volume: Math.max(0, layer.volume - 5),
                      })
                    }
                    className="p-1 bg-amber-600/20 hover:bg-amber-600/40 rounded"
                  >
                    <Minus size={12} className="text-amber-400" />
                  </button>
                  <span className="text-xs text-white w-12 text-center">{layer.volume}%</span>
                  <button
                    onClick={() =>
                      onUpdateLayer(layer.id, {
                        volume: Math.min(100, layer.volume + 5),
                      })
                    }
                    className="p-1 bg-amber-600/20 hover:bg-amber-600/40 rounded"
                  >
                    <Plus size={12} className="text-amber-400" />
                  </button>
                </div>
              </div>
            ))}

            {/* Brownian Noise Layer */}
            <div className="bg-orange-900/30 rounded-lg p-3 flex-shrink-0 border border-orange-500/30">
              <div className="flex items-center justify-between mb-2">
                <span className="text-orange-100 text-sm font-medium">Layer 4 - Brownian Noise</span>
                <span className="text-xs text-gray-400">{brownianLayer.volume}% volume</span>
              </div>

              {/* Status */}
              <div className="flex items-center space-x-2 mb-2">
                <span className="text-xs text-gray-300 w-16">Status:</span>
                <span className={`text-xs ${brownianLayer.isActive ? "text-green-400" : "text-red-400"}`}>
                  {brownianLayer.isActive ? "Active" : "Inactive"}
                </span>
              </div>

              {/* Volume control */}
              <div className="flex items-center space-x-2">
                <span className="text-xs text-gray-300 w-16">Volume:</span>
                <button
                  onClick={() =>
                    onUpdateBrownianLayer({
                      volume: Math.max(0, brownianLayer.volume - 5),
                    })
                  }
                  className="p-1 bg-orange-600/20 hover:bg-orange-600/40 rounded"
                >
                  <Minus size={12} className="text-orange-400" />
                </button>
                <span className="text-xs text-white w-12 text-center">{brownianLayer.volume}%</span>
                <button
                  onClick={() =>
                    onUpdateBrownianLayer({
                      volume: Math.min(100, brownianLayer.volume + 5),
                    })
                  }
                  className="p-1 bg-orange-600/20 hover:bg-orange-600/40 rounded"
                >
                  <Plus size={12} className="text-orange-400" />
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </motion.div>
  )
}
