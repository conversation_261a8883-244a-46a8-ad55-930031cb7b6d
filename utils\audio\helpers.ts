import * as Tone from "tone"
import { Spa<PERSON><PERSON>ay<PERSON>, <PERSON>ianLayer } from "../../constants/types"

/**
 * Initialize Tone.js audio context
 */
export async function initializeAudioContext(): Promise<void> {
  if (Tone.context.state !== "running") {
    await Tone.start()
  }
}

/**
 * Calculate audio levels for visualization
 */
export function calculateAudioLevels(
  spatialLayers: SpatialLayer[],
  brownianLayer: BrownianLayer,
  isLayered: boolean,
  isBrownianActive: boolean
) {
  // Calculate theoretical dB levels based on volume percentages and activity
  const layer1Level = spatialLayers[0] ? -30 + (spatialLayers[0].volume / 100) * 25 : -60
  const layer2Level = spatialLayers[1] ? -30 + (spatialLayers[1].volume / 100) * 25 : -60
  const layer3Level = spatialLayers[2] ? -30 + (spatialLayers[2].volume / 100) * 25 : -60
  const brownianLevel = isBrownianActive ? -30 + (brownianLayer.volume / 100) * 25 : -60

  // Master level is combination of active layers
  const activeLayers = [
    isLayered ? layer1Level : -60,
    isLayered ? layer2Level : -60,
    isLayered ? layer3Level : -60,
    brownianLevel,
  ].filter((level) => level > -60)

  const masterLevel = activeLayers.length > 0 ? Math.max(-60, -12 + 10 * Math.log10(activeLayers.length)) : -60

  return {
    layer1: isLayered ? layer1Level : -60,
    layer2: isLayered ? layer2Level : -60,
    layer3: layer3Level,
    brownian: brownianLevel,
    master: masterLevel,
  }
}

/**
 * Create master audio chain with volume control and limiter
 */
export function createMasterAudioChain(): { volume: Tone.Volume; limiter: Tone.Limiter } {
  const masterVolume = new Tone.Volume(-3) // Increased from -12dB to -3dB for audible levels
  const masterLimiter = new Tone.Limiter(-1) // Limit at -1dB to prevent clipping

  masterVolume.chain(masterLimiter, Tone.Destination)

  return {
    volume: masterVolume,
    limiter: masterLimiter,
  }
}

/**
 * Create spatial audio oscillator with panning
 */
export function createSpatialOscillator(
  frequency: number,
  position: { x: number; y: number; z: number },
  masterVolume: Tone.Volume
): { oscillator: Tone.Oscillator; panner: Tone.Panner3D; volume: Tone.Volume } {
  const oscillator = new Tone.Oscillator({
    frequency,
    type: "sine",
    volume: -30,
  })

  const panner = new Tone.Panner3D({
    positionX: position.x,
    positionY: position.y,
    positionZ: position.z,
    orientationX: 0,
    orientationY: 0,
    orientationZ: -1,
  })

  const volume = new Tone.Volume(-30)

  // Connect through master volume/limiter chain
  oscillator.chain(volume, panner, masterVolume)

  return { oscillator, panner, volume }
}

/**
 * Create brownian noise with spatial positioning
 */
export function createBrownianNoise(
  masterVolume: Tone.Volume
): { noise: Tone.Noise; panner: Tone.Panner3D; volume: Tone.Volume } {
  const brownianNoise = new Tone.Noise({
    type: "brown",
    volume: -25,
  })

  const volume = new Tone.Volume(-25)
  const panner = new Tone.Panner3D({
    positionX: 0,
    positionY: 0,
    positionZ: 2,
    orientationX: 0,
    orientationY: 0,
    orientationZ: -1,
  })

  // Connect through master volume/limiter chain
  brownianNoise.chain(volume, panner, masterVolume)

  return { noise: brownianNoise, panner, volume }
}

/**
 * Create piano synthesizer with effects
 */
export function createPianoSynth(masterVolume: Tone.Volume): {
  piano: Tone.PolySynth
  volume: Tone.Volume
  reverb: Tone.Reverb
  chorus: Tone.Chorus
} {
  const piano = new Tone.PolySynth(Tone.Synth, {
    oscillator: {
      type: "sine",
    },
    envelope: {
      attack: 0.3,
      decay: 0.4,
      sustain: 0.6,
      release: 2.0,
    },
    volume: -25,
  })

  const pianoVolume = new Tone.Volume(-15)
  const pianoReverb = new Tone.Reverb({
    roomSize: 0.8,
    dampening: 1000,
    wet: 0.4,
  })
  const pianoChorus = new Tone.Chorus({
    frequency: 0.5,
    delayTime: 3.5,
    depth: 0.3,
    wet: 0.2,
  })

  // Chain piano through effects to master
  piano.chain(pianoVolume, pianoChorus, pianoReverb, masterVolume)

  return {
    piano,
    volume: pianoVolume,
    reverb: pianoReverb,
    chorus: pianoChorus,
  }
}

/**
 * Calculate volume for spatial oscillators
 */
export function calculateSpatialVolume(
  layerVolume: number,
  totalOscillators: number,
  baseVolume: number = -20
): number {
  const layerVolumeMultiplier = layerVolume / 100
  const oscillatorReduction = Math.log10(totalOscillators) * 6 // Reduced reduction
  const finalVolume = baseVolume + layerVolumeMultiplier * 20 - oscillatorReduction

  return Math.max(-40, finalVolume) // Higher minimum volume
}

/**
 * Dispose of audio resources safely
 */
export function disposeAudioResource(resource: any): void {
  try {
    if (resource && typeof resource.dispose === "function") {
      resource.dispose()
    }
  } catch (error) {
    console.warn("Error disposing audio resource:", error)
  }
}

/**
 * Stop audio resource safely
 */
export function stopAudioResource(resource: any): void {
  try {
    if (resource && typeof resource.stop === "function" && resource.state === "started") {
      resource.stop()
    }
  } catch (error) {
    console.warn("Error stopping audio resource:", error)
  }
}
