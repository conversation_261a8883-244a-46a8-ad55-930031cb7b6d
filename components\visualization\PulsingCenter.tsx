"use client"

import { useRef } from "react"
import { use<PERSON>rame } from "@react-three/fiber"
import { THREE } from "../../utils/three"

export function PulsingCenter() {
  const meshRef = useRef<THREE.Mesh>(null)

  useFrame((state) => {
    if (meshRef.current) {
      const pulse = 1 + Math.sin(state.clock.elapsedTime * 3) * 0.3
      meshRef.current.scale.setScalar(pulse * 0.02)
    }
  })

  return (
    <mesh ref={meshRef}>
      <sphereGeometry args={[1, 16, 16]} />
      <meshBasicMaterial color="white" />
    </mesh>
  )
}
