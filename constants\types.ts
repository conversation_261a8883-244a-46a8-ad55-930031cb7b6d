import { THREE } from "../utils/three"

export interface Ring {
  id: number
  scale: number
  opacity: number
  frequency: number
  label: string
  color: THREE.Color
  rippleStrength: number
}

export interface SpatialLayer {
  id: number
  distance: number // Distance from center in 3D units
  volume: number // Volume percentage
  positions: Array<{
    x: number
    y: number
    z: number
    angle: number
    label: string
    isActive: boolean
  }>
}

export interface BrownianLayer {
  id: number
  volume: number
  isActive: boolean
}

export interface AudioLevels {
  layer1: number
  layer2: number
  layer3: number
  brownian: number
  master: number
}

export interface InteractionState {
  isActive: boolean
  intensity: number
  frequency: number
  vowel: string
  activeLayerCount: number
}

export interface FrequencyData {
  freq: number
  label: string
  duration: number
}

export interface ChordData {
  notes: string[]
  duration: number
  pause: boolean
}

export interface ChordProgression {
  bpm: number
  chords: ChordData[]
}
