// Centralized Three.js exports to prevent multiple instances
// This ensures we have a single Three.js instance across the entire app

export * from 'three'
export { default as THREE } from 'three'

// Re-export commonly used Three.js types and classes
export type {
  Vector2,
  Vector3,
  Color,
  Mesh,
  Group,
  Object3D,
  Camera,
  Scene,
  WebGLRenderer,
  BufferGeometry,
  Material,
  Texture
} from 'three'
