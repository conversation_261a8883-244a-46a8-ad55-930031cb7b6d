"use client"

import { motion, AnimatePresence } from "framer-motion"
import { FREQUENCIES, CHORD_PROGRESSIONS } from "../../../constants/frequencies"
import { Tooltip } from "./Tooltip"

interface ControlPanelProps {
  showControls: boolean
  mode: string
  isLayered: boolean
  isBrownianActive: boolean
  isPianoActive: boolean
  pianoProgression: string
  interactionState: {
    isActive: boolean
    intensity: number
    frequency: number
    vowel: string
    activeLayerCount: number
  }
  showSpatialViz: boolean
  onModeChange: (mode: string) => void
  onLayeredToggle: () => void
  onBrownianToggle: () => void
  onPianoToggle: () => void
  onPianoProgressionChange: (progression: string) => void
  onSpatialVizToggle: () => void
}

export function ControlPanel({
  showControls,
  mode,
  isLayered,
  isBrownianActive,
  isPianoActive,
  pianoProgression,
  interactionState,
  showSpatialViz,
  onModeChange,
  onLayeredToggle,
  onBrownianToggle,
  onPianoToggle,
  onPianoProgressionChange,
  onSpatialVizToggle,
}: ControlPanelProps) {
  // Tooltip content for each mode
  const getTooltipContent = (modeKey: string) => {
    const frequencies = FREQUENCIES[modeKey as keyof typeof FREQUENCIES]
    if (!frequencies) return ""

    return frequencies.map((freq, index) => `${index + 1}. ${freq.label}`).join("\n")
  }

  const layeredTooltipContent = `Creates immersive 3D spatial audio experience:
• 3 concentric layers with 5 positions each (15 total)
• Layer distances: 2", 5", 9" from center
• Volume distribution adjustable (should total 100%)
• HRTF processing for realistic spatial perception
• Real-time dB monitoring and adjustable volumes`

  const brownianTooltipContent = "Brownian Noise Layers for soothing effect"

  const voiceTooltipContent = `Interactive Ethereal Voice Synthesis:
• Click and drag to sing harmonic notes
• 5-layer voice synthesis with formant filtering
• Real-time vowel morphing (Y-axis: u→o→a→e→i)
• Cathedral reverb and ping-pong delay
• Intensity builds with hold duration
• Breathiness and vibrato for realism`

  return (
    <div className="absolute top-0 left-0 right-0 z-10 p-6 pointer-events-auto">
      <AnimatePresence>
        {showControls && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="flex flex-col items-center space-y-4"
          >
            {/* Mode Selection */}
            <div className="flex justify-center space-x-4">
              {[
                { key: "normal", label: "Normal" },
                { key: "pleasure", label: "Pleasure" },
                { key: "antiPain", label: "Anti Pain Extreme" },
              ].map(({ key, label }) => (
                <Tooltip key={key} content={getTooltipContent(key)}>
                  <motion.button
                    onClick={() => onModeChange(key)}
                    className={`px-6 py-3 rounded-full backdrop-blur-sm border transition-all ${
                      mode === key
                        ? "bg-cyan-400/20 border-cyan-400/50 text-cyan-100"
                        : "bg-white/10 border-white/20 text-white/80 hover:bg-white/20"
                    }`}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    {label}
                  </motion.button>
                </Tooltip>
              ))}
              
              {/* Brownian Noise Toggle */}
              <Tooltip content={brownianTooltipContent}>
                <motion.button
                  onClick={onBrownianToggle}
                  className={`px-3 py-3 rounded-full backdrop-blur-sm border transition-all ${
                    isBrownianActive
                      ? "bg-amber-400/20 border-amber-400/50 text-amber-100"
                      : "bg-white/10 border-white/20 text-white/80 hover:bg-white/20"
                  }`}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  🌊
                </motion.button>
              </Tooltip>

              {/* Piano Progression Dropdown */}
              <div className="relative">
                <Tooltip content="Select gentle piano chord progression">
                  <motion.button
                    onClick={onPianoToggle}
                    className={`px-3 py-3 rounded-full backdrop-blur-sm border transition-all ${
                      isPianoActive
                        ? "bg-green-400/20 border-green-400/50 text-green-100"
                        : "bg-white/10 border-white/20 text-white/80 hover:bg-white/20"
                    }`}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    🎹
                  </motion.button>
                </Tooltip>

                {isPianoActive && (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="absolute top-full left-0 mt-2 bg-black/90 backdrop-blur-sm border border-white/20 rounded-lg overflow-hidden z-20"
                  >
                    {Object.entries(CHORD_PROGRESSIONS).map(([key, progression]) => (
                      <button
                        key={key}
                        onClick={() => onPianoProgressionChange(key)}
                        className={`block w-full px-4 py-2 text-left text-sm transition-colors ${
                          pianoProgression === key
                            ? "bg-green-400/20 text-green-100"
                            : "text-white/80 hover:bg-white/10"
                        }`}
                      >
                        {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                      </button>
                    ))}
                  </motion.div>
                )}
              </div>

              {/* Voice Synthesis Indicator */}
              <Tooltip content={voiceTooltipContent}>
                <motion.div
                  className={`px-3 py-3 rounded-full backdrop-blur-sm border transition-all ${
                    interactionState.isActive
                      ? "bg-purple-400/20 border-purple-400/50 text-purple-100"
                      : "bg-white/10 border-white/20 text-white/80"
                  }`}
                >
                  🎤
                </motion.div>
              </Tooltip>
            </div>

            {/* Layered Toggle */}
            {mode !== "normal" && (
              <div className="flex items-center space-x-2">
                <Tooltip content={layeredTooltipContent}>
                  <motion.button
                    onClick={onLayeredToggle}
                    className={`px-6 py-2 rounded-full backdrop-blur-sm border transition-all ${
                      isLayered
                        ? "bg-purple-400/20 border-purple-400/50 text-purple-100"
                        : "bg-white/10 border-white/20 text-white/80 hover:bg-white/20"
                    }`}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.8 }}
                  >
                    {isLayered ? "🎧 Layered 3D" : "🔊 Stereo"}
                  </motion.button>
                </Tooltip>

                {/* Spatial Visualization Toggle */}
                {isLayered && (
                  <motion.button
                    onClick={onSpatialVizToggle}
                    className={`px-3 py-2 rounded-full backdrop-blur-sm border transition-all ${
                      showSpatialViz
                        ? "bg-green-400/20 border-green-400/50 text-green-100"
                        : "bg-white/10 border-white/20 text-white/80 hover:bg-white/20"
                    }`}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.8 }}
                  >
                    📊
                  </motion.button>
                )}
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
