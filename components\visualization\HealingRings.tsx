"use client"

import { useRef, useState, useMemo } from "react"
import { useFrame } from "@react-three/fiber"
import { Text } from "@react-three/drei"
import { THREE } from "../../utils/three"
import { FREQUENCIES } from "../../constants/frequencies"
import { Ring, InteractionState } from "../../constants/types"

interface HealingRingsProps {
  mode: string
  mousePosition: THREE.Vector2
  interactionState: InteractionState
}

export function HealingRings({ mode, mousePosition, interactionState }: HealingRingsProps) {
  const groupRef = useRef<THREE.Group>(null)
  const [rings, setRings] = useState<Ring[]>([])
  const [currentFreqIndex, setCurrentFreqIndex] = useState(0)
  const [nextRingTime, setNextRingTime] = useState(0)

  const frequencies = FREQUENCIES[mode as keyof typeof FREQUENCIES] || FREQUENCIES.normal

  // Color palette for healing
  const colors = useMemo(
    () => [
      new THREE.Color(0xfffff0), // Very soft white
      new THREE.Color(0xffffe0), // Very soft yellow
      new THREE.Color(0xe0ffff), // Very soft cyan
      new THREE.Color(0xe6e6fa), // Very soft blue
      new THREE.Color(0x191970), // Deep blue
      new THREE.Color(0xffe4e1), // Very gentle pink
    ],
    [],
  )

  useFrame((state) => {
    const time = state.clock.elapsedTime

    // Create new rings every 30 seconds to match audio timing
    if (time > nextRingTime) {
      const currentFreq = frequencies[currentFreqIndex]
      const newRing: Ring = {
        id: Date.now(),
        scale: 0.1,
        opacity: 1,
        frequency: currentFreq.freq,
        label: currentFreq.label,
        color: colors[Math.floor(Math.random() * colors.length)],
        rippleStrength: 0,
      }

      setRings((prev) => [...prev, newRing])
      setNextRingTime(time + 30) // New ring every 30 seconds to match audio
      setCurrentFreqIndex((prev) => (prev + 1) % frequencies.length)
    }

    // Update existing rings - slower expansion to match 30-second duration
    setRings((prev) =>
      prev
        .map((ring) => {
          const newScale = ring.scale + 0.003 // Slower expansion for 30-second duration
          const newOpacity = Math.max(0, 1 - (newScale - 0.1) / 12) // Adjusted for longer duration

          // Calculate ripple effect based on mouse proximity and interaction
          const ringWorldPos = new THREE.Vector3(0, 0, 0)
          const distance = mousePosition.distanceTo(new THREE.Vector2(ringWorldPos.x, ringWorldPos.y))
          let rippleStrength = Math.max(0, 1 - distance / 2) * 0.3

          // Enhance ripple effect during voice interaction
          if (interactionState.isActive) {
            rippleStrength += interactionState.intensity * 0.7
          }

          return {
            ...ring,
            scale: newScale,
            opacity: newOpacity,
            rippleStrength: rippleStrength,
          }
        })
        .filter((ring) => ring.opacity > 0.01),
    )
  })

  return (
    <group ref={groupRef}>
      {rings.map((ring) => (
        <RingComponent key={ring.id} ring={ring} />
      ))}
      {/* Interactive voice visualization */}
      {interactionState.isActive && (
        <VoiceVisualization
          position={mousePosition}
          intensity={interactionState.intensity}
          vowel={interactionState.vowel}
          activeLayerCount={interactionState.activeLayerCount}
        />
      )}
    </group>
  )
}

function RingComponent({ ring }: { ring: Ring }) {
  const meshRef = useRef<THREE.Mesh>(null)
  const textRef = useRef<any>(null)

  useFrame(() => {
    if (meshRef.current) {
      meshRef.current.scale.setScalar(ring.scale + ring.rippleStrength)
      const material = meshRef.current.material as THREE.MeshBasicMaterial
      material.opacity = ring.opacity
    }

    if (textRef.current) {
      textRef.current.scale.setScalar(ring.scale)
      textRef.current.material.opacity = ring.opacity * 0.8
    }
  })

  return (
    <group>
      <mesh ref={meshRef}>
        <ringGeometry args={[0.8, 1, 64]} />
        <meshBasicMaterial color={ring.color} transparent opacity={ring.opacity} side={THREE.DoubleSide} />
      </mesh>

      <Text
        ref={textRef}
        position={[0, 0.2, 0]}
        fontSize={0.08}
        color="white"
        anchorX="center"
        anchorY="middle"
        font="/fonts/Inter-Bold.ttf"
      >
        {ring.label}
      </Text>
    </group>
  )
}

function VoiceVisualization({
  position,
  intensity,
  vowel,
  activeLayerCount,
}: {
  position: THREE.Vector2
  intensity: number
  vowel: string
  activeLayerCount: number
}) {
  const meshRef = useRef<THREE.Mesh>(null)
  const layerRefs = useRef<THREE.Mesh[]>([])

  // Vowel colors for visualization
  const vowelColors = {
    a: new THREE.Color(0xff6b6b), // Red for "ah"
    e: new THREE.Color(0x4ecdc4), // Teal for "eh"
    i: new THREE.Color(0x45b7d1), // Blue for "ee"
    o: new THREE.Color(0xf9ca24), // Yellow for "oh"
    u: new THREE.Color(0x6c5ce7), // Purple for "oo"
  }

  useFrame((state) => {
    // Main voice sphere
    if (meshRef.current) {
      const scale = 0.2 + intensity * 0.5
      meshRef.current.scale.setScalar(scale)
      meshRef.current.position.set(position.x * 3, position.y * 3, 0)

      // Breathing effect
      const breathe = 1 + Math.sin(state.clock.elapsedTime * 4) * intensity * 0.2
      meshRef.current.scale.multiplyScalar(breathe)

      // Update color based on vowel
      const material = meshRef.current.material as THREE.MeshBasicMaterial
      const targetColor = vowelColors[vowel as keyof typeof vowelColors] || vowelColors.a
      material.color.lerp(targetColor, 0.1)
    }

    // Voice layer visualization
    layerRefs.current.forEach((mesh, index) => {
      if (mesh && index < activeLayerCount) {
        const angle = (index / activeLayerCount) * Math.PI * 2 + state.clock.elapsedTime * 0.5
        const radius = 0.8 + intensity * 0.4
        const x = position.x * 3 + Math.cos(angle) * radius
        const y = position.y * 3 + Math.sin(angle) * radius
        const z = Math.sin(state.clock.elapsedTime * 2 + index) * 0.3

        mesh.position.set(x, y, z)

        const scale = 0.08 + intensity * 0.12
        mesh.scale.setScalar(scale)

        // Harmonic layer colors
        const hue = (index / 5) * 0.6 + 0.2 // Rainbow spectrum
        const material = mesh.material as THREE.MeshBasicMaterial
        material.color.setHSL(hue, 0.7, 0.6)
      }
    })
  })

  return (
    <group>
      {/* Main voice sphere */}
      <mesh ref={meshRef}>
        <sphereGeometry args={[1, 16, 16]} />
        <meshBasicMaterial transparent opacity={0.7 + intensity * 0.2} />
      </mesh>

      {/* Voice layer spheres */}
      {Array.from({ length: 5 }, (_, index) => (
        <mesh
          key={index}
          ref={(el) => {
            if (el) layerRefs.current[index] = el
          }}
        >
          <sphereGeometry args={[0.5, 8, 8]} />
          <meshBasicMaterial transparent opacity={0.5} />
        </mesh>
      ))}
    </group>
  )
}
