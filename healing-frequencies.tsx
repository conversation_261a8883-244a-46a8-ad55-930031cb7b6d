"use client"

import type React from "react"

import { useRef, useState, useEffect, useMemo } from "react"
import { Canvas, useFrame } from "@react-three/fiber"
import { Text, Environment } from "@react-three/drei"
import { motion, AnimatePresence } from "framer-motion"
import * as THRE<PERSON> from "three"
import * as To<PERSON> from "tone"
import { Minus, Plus, Maximize2, Minimize2, Move } from "lucide-react"
import { InteractiveAudioHandler } from "./lib/interactive-audio"

// Healing frequency data based on research - extended to 30 seconds
const FREQUENCIES = {
  normal: [
    { freq: 528, label: "528 Hz - Love & DNA Repair", duration: 30 },
    { freq: 432, label: "432 Hz - Natural Harmony", duration: 30 },
    { freq: 396, label: "396 Hz - Liberation", duration: 30 },
  ],
  pleasure: [
    { freq: 40, label: "40 Hz - Gamma Bliss", duration: 30 },
    { freq: 10, label: "10 Hz - Alpha Pleasure", duration: 30 },
    { freq: 528, label: "528 Hz - Love Frequency", duration: 30 },
    { freq: 741, label: "741 Hz - Consciousness", duration: 30 },
  ],
  antiPain: [
    { freq: 174, label: "174 Hz - Pain Relief", duration: 30 },
    { freq: 285, label: "285 Hz - Tissue Healing", duration: 30 },
    { freq: 396, label: "396 Hz - Pain Liberation", duration: 30 },
    { freq: 528, label: "528 Hz - Cellular Repair", duration: 30 },
  ],
}

// Add these chord progression definitions after the FREQUENCIES constant
const CHORD_PROGRESSIONS = {
  gentleWorship: {
    name: "Gentle Worship",
    bpm: 72,
    chords: [
      { notes: ["C4", "E4", "G4", "B4"], duration: 4, pause: false }, // Cmaj7
      { notes: ["A3", "C4", "E4", "A4"], duration: 4, pause: false }, // Am
      { notes: ["F3", "A3", "C4", "F4"], duration: 4, pause: false }, // F
      { notes: ["G3", "B3", "D4", "G4"], duration: 2, pause: false }, // G
      { notes: [], duration: 2, pause: true }, // Pause
      { notes: ["E3", "G#3", "B3", "E4"], duration: 4, pause: false }, // E
      { notes: ["A3", "C4", "E4", "A4"], duration: 4, pause: false }, // Am
      { notes: ["D3", "F#3", "A3", "D4"], duration: 4, pause: false }, // D
    ],
  },
  euphoricGospel: {
    name: "Euphoric Gospel",
    bpm: 80,
    chords: [
      { notes: ["C4", "E4", "G4", "B4"], duration: 3, pause: false }, // Cmaj7
      { notes: ["F3", "A3", "C4", "E4"], duration: 3, pause: false }, // Fmaj7
      { notes: ["G3", "B3", "D4", "F4"], duration: 2, pause: false }, // G7
      { notes: [], duration: 2, pause: true }, // Pause
      { notes: ["A3", "C#4", "E4", "G4"], duration: 3, pause: false }, // A7
      { notes: ["D3", "F#3", "A3", "C4"], duration: 3, pause: false }, // D7
      { notes: ["G3", "B3", "D4", "G4"], duration: 4, pause: false }, // G
      { notes: [], duration: 2, pause: true }, // Pause
    ],
  },
  ephemeralForest: {
    name: "Ephemeral Forest",
    bpm: 65,
    chords: [
      { notes: ["C4", "F4", "G4", "D5"], duration: 6, pause: false }, // Csus4add9 - mystical opening
      { notes: ["A3", "D4", "E4", "B4"], duration: 4, pause: false }, // Asus4add9 - forest whispers
      { notes: ["F3", "Bb3", "C4", "G4"], duration: 5, pause: false }, // Fsus4add9 - ancient trees
      { notes: [], duration: 3, pause: true }, // Pause - forest silence
      { notes: ["G3", "C4", "D4", "A4"], duration: 4, pause: false }, // Gsus4add9 - wind through leaves
      { notes: ["E3", "A3", "B3", "F#4"], duration: 5, pause: false }, // Esus4add9 - ethereal light
      { notes: ["D3", "G3", "A3", "E4"], duration: 6, pause: false }, // Dsus4add9 - fading into mist
    ],
  },
  timeEscape: {
    name: "Time Escape",
    bpm: 60,
    chords: [
      { notes: ["Bb3", "Eb4", "F4", "C5"], duration: 8, pause: false }, // Bbsus4add9 - floating in time
      { notes: ["Ab3", "Db4", "Eb4", "Bb4"], duration: 6, pause: false }, // Absus4add9 - temporal drift
      { notes: [], duration: 4, pause: true }, // Pause - timeless void
      { notes: ["F3", "Bb3", "C4", "G4"], duration: 7, pause: false }, // Fsus4add9 - memories surfacing
      { notes: ["Eb3", "Ab3", "Bb3", "F4"], duration: 5, pause: false }, // Ebsus4add9 - distant echoes
      { notes: ["Db3", "Gb3", "Ab3", "Eb4"], duration: 6, pause: false }, // Dbsus4add9 - time suspension
      { notes: ["C3", "F3", "G3", "D4"], duration: 8, pause: false }, // Csus4add9 - return to present
    ],
  },
  gentleRiver: {
    name: "Gentle River",
    bpm: 68,
    chords: [
      { notes: ["D4", "G4", "A4", "E5"], duration: 5, pause: false }, // Dsus4add9 - river source
      { notes: ["B3", "E4", "F#4", "C#5"], duration: 4, pause: false }, // Bsus4add9 - gentle flow
      { notes: ["G3", "C4", "D4", "A4"], duration: 5, pause: false }, // Gsus4add9 - meandering stream
      { notes: ["E3", "A3", "B3", "F#4"], duration: 4, pause: false }, // Esus4add9 - over smooth stones
      { notes: [], duration: 2, pause: true }, // Pause - still pool
      { notes: ["A3", "D4", "E4", "B4"], duration: 6, pause: false }, // Asus4add9 - continuing journey
      { notes: ["F#3", "B3", "C#4", "G#4"], duration: 5, pause: false }, // F#sus4add9 - to the sea
    ],
  },
}

interface Ring {
  id: number
  scale: number
  opacity: number
  frequency: number
  label: string
  color: THREE.Color
  rippleStrength: number
}

interface SpatialLayer {
  id: number
  distance: number // Distance from center in 3D units
  volume: number // Volume percentage
  positions: Array<{
    x: number
    y: number
    z: number
    angle: number
    label: string
    isActive: boolean
  }>
}

interface BrownianLayer {
  id: number
  volume: number
  isActive: boolean
}

interface AudioLevels {
  layer1: number
  layer2: number
  layer3: number
  brownian: number
  master: number
}

function HealingRings({
  mode,
  mousePosition,
  interactionState,
}: {
  mode: string
  mousePosition: THREE.Vector2
  interactionState: {
    isActive: boolean
    intensity: number
    frequency: number
    vowel: string
    activeLayerCount: number
  }
}) {
  const groupRef = useRef<THREE.Group>(null)
  const [rings, setRings] = useState<Ring[]>([])
  const [currentFreqIndex, setCurrentFreqIndex] = useState(0)
  const [nextRingTime, setNextRingTime] = useState(0)

  const frequencies = FREQUENCIES[mode as keyof typeof FREQUENCIES] || FREQUENCIES.normal

  // Color palette for healing
  const colors = useMemo(
    () => [
      new THREE.Color(0xfffff0), // Very soft white
      new THREE.Color(0xffffe0), // Very soft yellow
      new THREE.Color(0xe0ffff), // Very soft cyan
      new THREE.Color(0xe6e6fa), // Very soft blue
      new THREE.Color(0x191970), // Deep blue
      new THREE.Color(0xffe4e1), // Very gentle pink
    ],
    [],
  )

  useFrame((state) => {
    const time = state.clock.elapsedTime

    // Create new rings every 30 seconds to match audio timing
    if (time > nextRingTime) {
      const currentFreq = frequencies[currentFreqIndex]
      const newRing: Ring = {
        id: Date.now(),
        scale: 0.1,
        opacity: 1,
        frequency: currentFreq.freq,
        label: currentFreq.label,
        color: colors[Math.floor(Math.random() * colors.length)],
        rippleStrength: 0,
      }

      setRings((prev) => [...prev, newRing])
      setNextRingTime(time + 30) // New ring every 30 seconds to match audio
      setCurrentFreqIndex((prev) => (prev + 1) % frequencies.length)
    }

    // Update existing rings - slower expansion to match 30-second duration
    setRings((prev) =>
      prev
        .map((ring) => {
          const newScale = ring.scale + 0.003 // Slower expansion for 30-second duration
          const newOpacity = Math.max(0, 1 - (newScale - 0.1) / 12) // Adjusted for longer duration

          // Calculate ripple effect based on mouse proximity and interaction
          const ringWorldPos = new THREE.Vector3(0, 0, 0)
          const distance = mousePosition.distanceTo(new THREE.Vector2(ringWorldPos.x, ringWorldPos.y))
          let rippleStrength = Math.max(0, 1 - distance / 2) * 0.3

          // Enhance ripple effect during voice interaction
          if (interactionState.isActive) {
            rippleStrength += interactionState.intensity * 0.7
          }

          return {
            ...ring,
            scale: newScale,
            opacity: newOpacity,
            rippleStrength: rippleStrength,
          }
        })
        .filter((ring) => ring.opacity > 0.01),
    )
  })

  return (
    <group ref={groupRef}>
      {rings.map((ring) => (
        <RingComponent key={ring.id} ring={ring} />
      ))}
      {/* Interactive voice visualization */}
      {interactionState.isActive && (
        <VoiceVisualization
          position={mousePosition}
          intensity={interactionState.intensity}
          vowel={interactionState.vowel}
          activeLayerCount={interactionState.activeLayerCount}
        />
      )}
    </group>
  )
}

function VoiceVisualization({
  position,
  intensity,
  vowel,
  activeLayerCount,
}: {
  position: THREE.Vector2
  intensity: number
  vowel: string
  activeLayerCount: number
}) {
  const meshRef = useRef<THREE.Mesh>(null)
  const layerRefs = useRef<THREE.Mesh[]>([])

  // Vowel colors for visualization
  const vowelColors = {
    a: new THREE.Color(0xff6b6b), // Red for "ah"
    e: new THREE.Color(0x4ecdc4), // Teal for "eh"
    i: new THREE.Color(0x45b7d1), // Blue for "ee"
    o: new THREE.Color(0xf9ca24), // Yellow for "oh"
    u: new THREE.Color(0x6c5ce7), // Purple for "oo"
  }

  useFrame((state) => {
    // Main voice sphere
    if (meshRef.current) {
      const scale = 0.2 + intensity * 0.5
      meshRef.current.scale.setScalar(scale)
      meshRef.current.position.set(position.x * 3, position.y * 3, 0)

      // Breathing effect
      const breathe = 1 + Math.sin(state.clock.elapsedTime * 4) * intensity * 0.2
      meshRef.current.scale.multiplyScalar(breathe)

      // Update color based on vowel
      const material = meshRef.current.material as THREE.MeshBasicMaterial
      const targetColor = vowelColors[vowel as keyof typeof vowelColors] || vowelColors.a
      material.color.lerp(targetColor, 0.1)
    }

    // Voice layer visualization
    layerRefs.current.forEach((mesh, index) => {
      if (mesh && index < activeLayerCount) {
        const angle = (index / activeLayerCount) * Math.PI * 2 + state.clock.elapsedTime * 0.5
        const radius = 0.8 + intensity * 0.4
        const x = position.x * 3 + Math.cos(angle) * radius
        const y = position.y * 3 + Math.sin(angle) * radius
        const z = Math.sin(state.clock.elapsedTime * 2 + index) * 0.3

        mesh.position.set(x, y, z)

        const scale = 0.08 + intensity * 0.12
        mesh.scale.setScalar(scale)

        // Harmonic layer colors
        const hue = (index / 5) * 0.6 + 0.2 // Rainbow spectrum
        const material = mesh.material as THREE.MeshBasicMaterial
        material.color.setHSL(hue, 0.7, 0.6)
      }
    })
  })

  return (
    <group>
      {/* Main voice sphere */}
      <mesh ref={meshRef}>
        <sphereGeometry args={[1, 16, 16]} />
        <meshBasicMaterial transparent opacity={0.7 + intensity * 0.2} />
      </mesh>

      {/* Voice layer spheres */}
      {Array.from({ length: 5 }, (_, index) => (
        <mesh
          key={index}
          ref={(el) => {
            if (el) layerRefs.current[index] = el
          }}
        >
          <sphereGeometry args={[0.5, 8, 8]} />
          <meshBasicMaterial transparent opacity={0.5} />
        </mesh>
      ))}
    </group>
  )
}

function RingComponent({ ring }: { ring: Ring }) {
  const meshRef = useRef<THREE.Mesh>(null)
  const textRef = useRef<any>(null)

  useFrame(() => {
    if (meshRef.current) {
      meshRef.current.scale.setScalar(ring.scale + ring.rippleStrength)
      const material = meshRef.current.material as THREE.MeshBasicMaterial
      material.opacity = ring.opacity
    }

    if (textRef.current) {
      textRef.current.scale.setScalar(ring.scale)
      textRef.current.material.opacity = ring.opacity * 0.8
    }
  })

  return (
    <group>
      <mesh ref={meshRef}>
        <ringGeometry args={[0.8, 1, 64]} />
        <meshBasicMaterial color={ring.color} transparent opacity={ring.opacity} side={THREE.DoubleSide} />
      </mesh>

      <Text
        ref={textRef}
        position={[0, 0.2, 0]}
        fontSize={0.08}
        color="white"
        anchorX="center"
        anchorY="middle"
        font="/fonts/Inter-Bold.ttf"
      >
        {ring.label}
      </Text>
    </group>
  )
}

function PulsingCenter() {
  const meshRef = useRef<THREE.Mesh>(null)

  useFrame((state) => {
    if (meshRef.current) {
      const pulse = 1 + Math.sin(state.clock.elapsedTime * 3) * 0.3
      meshRef.current.scale.setScalar(pulse * 0.02)
    }
  })

  return (
    <mesh ref={meshRef}>
      <sphereGeometry args={[1, 16, 16]} />
      <meshBasicMaterial color="white" />
    </mesh>
  )
}

// dB Level Meter Component
function DbMeter({ label, level, color = "cyan" }: { label: string; level: number; color?: string }) {
  // Convert dB to percentage for visual display (-60dB = 0%, 0dB = 100%)
  const percentage = Math.max(0, Math.min(100, ((level + 60) / 60) * 100))

  const getColorClass = () => {
    if (level > -6) return "bg-red-500" // Danger zone
    if (level > -12) return "bg-yellow-500" // Warning zone
    if (level > -24) return "bg-green-500" // Good zone
    return "bg-blue-500" // Quiet zone
  }

  const getTextColor = () => {
    if (level > -6) return "text-red-400"
    if (level > -12) return "text-yellow-400"
    if (level > -24) return "text-green-400"
    return "text-blue-400"
  }

  return (
    <div className="flex items-center space-x-2 mb-2">
      <span className="text-xs text-gray-300 w-16">{label}:</span>
      <div className="flex-1 bg-gray-700 rounded-full h-3 relative overflow-hidden">
        <div className={`h-full transition-all duration-300 ${getColorClass()}`} style={{ width: `${percentage}%` }} />
        {/* dB markers */}
        <div className="absolute inset-0 flex justify-between items-center px-1">
          <span className="text-xs text-white/50">-60</span>
          <span className="text-xs text-white/50">-30</span>
          <span className="text-xs text-white/50">0</span>
        </div>
      </div>
      <span className={`text-xs w-12 text-right ${getTextColor()}`}>{level.toFixed(1)}dB</span>
    </div>
  )
}

// Spatial Audio Visualization Component
function SpatialVisualization({
  isVisible,
  onToggle,
  layers,
  brownianLayer,
  onUpdateLayer,
  onUpdateBrownianLayer,
  activePositions,
  audioLevels,
}: {
  isVisible: boolean
  onToggle: () => void
  layers: SpatialLayer[]
  brownianLayer: BrownianLayer
  onUpdateLayer: (layerId: number, updates: Partial<SpatialLayer>) => void
  onUpdateBrownianLayer: (updates: Partial<BrownianLayer>) => void
  activePositions: Set<string>
  audioLevels: AudioLevels
}) {
  const [isMinimized, setIsMinimized] = useState(false)
  const [isDragging, setIsDragging] = useState(false)
  const [position, setPosition] = useState({ x: 20, y: 20 })
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true)
    setDragStart({
      x: e.clientX - position.x,
      y: e.clientY - position.y,
    })
  }

  const handleMouseMove = (e: MouseEvent) => {
    if (isDragging) {
      setPosition({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y,
      })
    }
  }

  const handleMouseUp = () => {
    setIsDragging(false)
  }

  useEffect(() => {
    if (isDragging) {
      document.addEventListener("mousemove", handleMouseMove)
      document.addEventListener("mouseup", handleMouseUp)
      return () => {
        document.removeEventListener("mousemove", handleMouseMove)
        document.removeEventListener("mouseup", handleMouseUp)
      }
    }
  }, [isDragging, dragStart])

  // Calculate total volume to ensure it equals 100%
  const totalVolume = layers.reduce((sum, layer) => sum + layer.volume, 0) + brownianLayer.volume
  const volumeWarning = Math.abs(totalVolume - 100) > 0.1

  if (!isVisible) return null

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.8 }}
      className="fixed bg-black/90 backdrop-blur-sm border border-cyan-400/30 rounded-lg shadow-2xl z-50"
      style={{
        left: position.x,
        top: position.y,
        width: isMinimized ? "200px" : "450px",
        height: isMinimized ? "40px" : "700px",
      }}
    >
      {/* Header */}
      <div
        className="flex items-center justify-between p-3 border-b border-cyan-400/20 cursor-move"
        onMouseDown={handleMouseDown}
      >
        <div className="flex items-center space-x-2">
          <Move size={16} className="text-cyan-400" />
          <span className="text-cyan-100 text-sm font-medium">3D Audio Control</span>
        </div>
        <div className="flex items-center space-x-1">
          <button onClick={() => setIsMinimized(!isMinimized)} className="p-1 hover:bg-cyan-400/20 rounded">
            {isMinimized ? (
              <Maximize2 size={14} className="text-cyan-400" />
            ) : (
              <Minimize2 size={14} className="text-cyan-400" />
            )}
          </button>
          <button onClick={onToggle} className="p-1 hover:bg-red-400/20 rounded text-red-400">
            ×
          </button>
        </div>
      </div>

      {/* Content */}
      {!isMinimized && (
        <div className="flex flex-col h-full">
          {/* dB Level Meters */}
          <div className="bg-gray-900/50 rounded-lg p-4 m-4 mb-2 flex-shrink-0">
            <div className="text-xs text-cyan-300 mb-3">Audio Level Monitor</div>
            <DbMeter label="Layer 1" level={audioLevels.layer1} />
            <DbMeter label="Layer 2" level={audioLevels.layer2} />
            <DbMeter label="Layer 3" level={audioLevels.layer3} />
            <DbMeter label="Brownian" level={audioLevels.brownian} />
            <div className="border-t border-gray-600 pt-2 mt-2">
              <DbMeter label="Master" level={audioLevels.master} />
            </div>
          </div>

          {/* Volume Warning */}
          {volumeWarning && (
            <div className="bg-red-900/50 border border-red-500/50 rounded-lg p-2 m-4 mb-2">
              <div className="text-red-300 text-xs text-center">
                Total Volume: {totalVolume.toFixed(1)}% (Should be 100%)
              </div>
            </div>
          )}

          {/* Top-down view - fixed height */}
          <div className="relative bg-gray-900/50 rounded-lg p-4 h-48 flex-shrink-0 mx-4">
            <div className="text-xs text-cyan-300 mb-2">Top-Down View</div>
            <svg viewBox="-100 -100 200 200" className="w-full h-full">
              {/* Center point (user) */}
              <circle cx="0" cy="0" r="3" fill="#ffffff" />
              <text x="0" y="-8" textAnchor="middle" className="text-xs fill-white">
                You
              </text>

              {/* Draw layers */}
              {layers.map((layer, layerIndex) => (
                <g key={layer.id}>
                  {/* Layer circle */}
                  <circle
                    cx="0"
                    cy="0"
                    r={layer.distance * 20}
                    fill="none"
                    stroke="#00ffff"
                    strokeWidth="0.5"
                    opacity="0.3"
                  />

                  {/* Audio positions */}
                  {layer.positions.map((pos, posIndex) => {
                    const x = Math.cos((pos.angle * Math.PI) / 180) * layer.distance * 20
                    const y = Math.sin((pos.angle * Math.PI) / 180) * layer.distance * 20
                    const isActive = activePositions.has(`${layer.id}-${posIndex}`)

                    return (
                      <circle
                        key={posIndex}
                        cx={x}
                        cy={y}
                        r={isActive ? "4" : "2"}
                        fill={isActive ? "#00ff00" : "#00ffff"}
                        className={isActive ? "animate-pulse" : ""}
                      />
                    )
                  })}

                  {/* Layer label */}
                  <text x={layer.distance * 20 + 5} y="0" className="text-xs fill-cyan-300">
                    L{layerIndex + 1}
                  </text>
                </g>
              ))}

              {/* Brownian noise indicator (center) */}
              {brownianLayer.isActive && (
                <circle
                  cx="0"
                  cy="0"
                  r="6"
                  fill="none"
                  stroke="#ffa500"
                  strokeWidth="2"
                  opacity="0.7"
                  className="animate-pulse"
                />
              )}
            </svg>
          </div>

          {/* Scrollable layer controls */}
          <div className="flex-1 overflow-y-auto p-4 space-y-3 min-h-0">
            {/* Spatial Layers */}
            {layers.map((layer, index) => (
              <div key={layer.id} className="bg-gray-800/50 rounded-lg p-3 flex-shrink-0">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-cyan-100 text-sm font-medium">Layer {index + 1}</span>
                  <span className="text-xs text-gray-400">{layer.volume}% volume</span>
                </div>

                {/* Distance control */}
                <div className="flex items-center space-x-2 mb-2">
                  <span className="text-xs text-gray-300 w-16">Distance:</span>
                  <button
                    onClick={() =>
                      onUpdateLayer(layer.id, {
                        distance: Math.max(0.5, layer.distance - 0.25),
                      })
                    }
                    className="p-1 bg-cyan-600/20 hover:bg-cyan-600/40 rounded"
                  >
                    <Minus size={12} className="text-cyan-400" />
                  </button>
                  <span className="text-xs text-white w-12 text-center">{(layer.distance * 4).toFixed(1)}"</span>
                  <button
                    onClick={() =>
                      onUpdateLayer(layer.id, {
                        distance: layer.distance + 0.25,
                      })
                    }
                    className="p-1 bg-cyan-600/20 hover:bg-cyan-600/40 rounded"
                  >
                    <Plus size={12} className="text-cyan-400" />
                  </button>
                </div>

                {/* Volume control */}
                <div className="flex items-center space-x-2">
                  <span className="text-xs text-gray-300 w-16">Volume:</span>
                  <button
                    onClick={() =>
                      onUpdateLayer(layer.id, {
                        volume: Math.max(0, layer.volume - 5),
                      })
                    }
                    className="p-1 bg-amber-600/20 hover:bg-amber-600/40 rounded"
                  >
                    <Minus size={12} className="text-amber-400" />
                  </button>
                  <span className="text-xs text-white w-12 text-center">{layer.volume}%</span>
                  <button
                    onClick={() =>
                      onUpdateLayer(layer.id, {
                        volume: Math.min(100, layer.volume + 5),
                      })
                    }
                    className="p-1 bg-amber-600/20 hover:bg-amber-600/40 rounded"
                  >
                    <Plus size={12} className="text-amber-400" />
                  </button>
                </div>
              </div>
            ))}

            {/* Brownian Noise Layer */}
            <div className="bg-orange-900/30 rounded-lg p-3 flex-shrink-0 border border-orange-500/30">
              <div className="flex items-center justify-between mb-2">
                <span className="text-orange-100 text-sm font-medium">Layer 4 - Brownian Noise</span>
                <span className="text-xs text-gray-400">{brownianLayer.volume}% volume</span>
              </div>

              {/* Status */}
              <div className="flex items-center space-x-2 mb-2">
                <span className="text-xs text-gray-300 w-16">Status:</span>
                <span className={`text-xs ${brownianLayer.isActive ? "text-green-400" : "text-red-400"}`}>
                  {brownianLayer.isActive ? "Active" : "Inactive"}
                </span>
              </div>

              {/* Volume control */}
              <div className="flex items-center space-x-2">
                <span className="text-xs text-gray-300 w-16">Volume:</span>
                <button
                  onClick={() =>
                    onUpdateBrownianLayer({
                      volume: Math.max(0, brownianLayer.volume - 5),
                    })
                  }
                  className="p-1 bg-orange-600/20 hover:bg-orange-600/40 rounded"
                >
                  <Minus size={12} className="text-orange-400" />
                </button>
                <span className="text-xs text-white w-12 text-center">{brownianLayer.volume}%</span>
                <button
                  onClick={() =>
                    onUpdateBrownianLayer({
                      volume: Math.min(100, brownianLayer.volume + 5),
                    })
                  }
                  className="p-1 bg-orange-600/20 hover:bg-orange-600/40 rounded"
                >
                  <Plus size={12} className="text-orange-400" />
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </motion.div>
  )
}

// Update the AudioSystem component props to include piano props
function AudioSystem({
  mode,
  isLayered,
  isBrownianActive,
  isPianoActive,
  pianoProgression,
  spatialLayers,
  brownianLayer,
  onUpdateActivePositions,
  onUpdateAudioLevels,
  interactiveHandler,
}: {
  mode: string
  isLayered: boolean
  isBrownianActive: boolean
  isPianoActive: boolean
  pianoProgression: string
  spatialLayers: SpatialLayer[]
  brownianLayer: BrownianLayer
  onUpdateActivePositions: (positions: Set<string>) => void
  onUpdateAudioLevels: (levels: AudioLevels) => void
  interactiveHandler: InteractiveAudioHandler | null
}) {
  const oscillatorsRef = useRef<Tone.Oscillator[]>([])
  const spatialPannersRef = useRef<Tone.Panner3D[]>([])
  const brownianNoiseRef = useRef<Tone.Noise | null>(null)
  const brownianPannerRef = useRef<Tone.Panner3D | null>(null)
  const brownianVolumeRef = useRef<Tone.Volume | null>(null)
  const currentIndexRef = useRef(0)
  const [isInitialized, setIsInitialized] = useState(false)

  // Add these refs after the existing refs in AudioSystem
  const masterLimiterRef = useRef<Tone.Limiter | null>(null)
  const masterVolumeRef = useRef<Tone.Volume | null>(null)
  const layerVolumeRefs = useRef<(Tone.Volume | null)[]>([null, null, null])

  // Add piano-related refs after the existing refs
  const pianoRef = useRef<Tone.PolySynth | null>(null)
  const pianoReverbRef = useRef<Tone.Reverb | null>(null)
  const pianoChorusRef = useRef<Tone.Chorus | null>(null)
  const pianoVolumeRef = useRef<Tone.Volume | null>(null)
  const pianoTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  const frequencies = FREQUENCIES[mode as keyof typeof FREQUENCIES] || FREQUENCIES.normal

  // Update interactive handler with current audio context and master volume
  useEffect(() => {
    if (interactiveHandler && masterVolumeRef.current) {
      const currentFreqs = frequencies.map((f) => f.freq)
      interactiveHandler.updateAudioContext(currentFreqs, mode, isPianoActive, pianoProgression)

      // Update the handler's master volume reference
      if ((interactiveHandler as any).masterVolume !== masterVolumeRef.current) {
        ;(interactiveHandler as any).masterVolume = masterVolumeRef.current
        // Re-initialize voice synthesizer with new master volume
        if ((interactiveHandler as any).initializeVoiceSynthesizer) {
          ;(interactiveHandler as any).initializeVoiceSynthesizer()
        }
      }
    }
  }, [interactiveHandler, frequencies, mode, isPianoActive, pianoProgression, isInitialized])

  // Update audio levels periodically
  useEffect(() => {
    const updateLevels = () => {
      if (!isInitialized) return

      // Calculate theoretical dB levels based on volume percentages and activity
      const layer1Level = spatialLayers[0] ? -30 + (spatialLayers[0].volume / 100) * 25 : -60
      const layer2Level = spatialLayers[1] ? -30 + (spatialLayers[1].volume / 100) * 25 : -60
      const layer3Level = spatialLayers[2] ? -30 + (spatialLayers[2].volume / 100) * 25 : -60
      const brownianLevel = isBrownianActive ? -30 + (brownianLayer.volume / 100) * 25 : -60

      // Master level is combination of active layers
      const activeLayers = [
        isLayered ? layer1Level : -60,
        isLayered ? layer2Level : -60,
        isLayered ? layer3Level : -60,
        brownianLevel,
      ].filter((level) => level > -60)

      const masterLevel = activeLayers.length > 0 ? Math.max(-60, -12 + 10 * Math.log10(activeLayers.length)) : -60

      onUpdateAudioLevels({
        layer1: isLayered ? layer1Level : -60,
        layer2: isLayered ? layer2Level : -60,
        layer3: layer3Level,
        brownian: brownianLevel,
        master: masterLevel,
      })
    }

    const interval = setInterval(updateLevels, 100) // Update 10 times per second
    return () => clearInterval(interval)
  }, [isInitialized, isLayered, isBrownianActive, spatialLayers, brownianLayer, onUpdateAudioLevels])

  // Initialize Tone.js - update this useEffect
  useEffect(() => {
    const initAudio = async () => {
      if (Tone.context.state !== "running") {
        await Tone.start()
      }

      // Create master limiter and volume control to prevent clipping
      if (!masterLimiterRef.current) {
        const masterVolume = new Tone.Volume(-3) // Increased from -12dB to -3dB for audible levels
        const masterLimiter = new Tone.Limiter(-1) // Limit at -1dB to prevent clipping

        masterVolume.chain(masterLimiter, Tone.Destination)

        masterVolumeRef.current = masterVolume
        masterLimiterRef.current = masterLimiter
      }

      // Add piano initialization in the main initialization useEffect, after the master effects setup
      // Create piano with warm, gentle sound and effects
      if (!pianoRef.current) {
        const piano = new Tone.PolySynth(Tone.Synth, {
          oscillator: {
            type: "sine",
          },
          envelope: {
            attack: 0.3,
            decay: 0.4,
            sustain: 0.6,
            release: 2.0,
          },
          volume: -25,
        })

        const pianoVolume = new Tone.Volume(-15)
        const pianoReverb = new Tone.Reverb({
          roomSize: 0.8,
          dampening: 1000,
          wet: 0.4,
        })
        const pianoChorus = new Tone.Chorus({
          frequency: 0.5,
          delayTime: 3.5,
          depth: 0.3,
          wet: 0.2,
        })

        // Chain piano through effects to master
        piano.chain(pianoVolume, pianoChorus, pianoReverb, masterVolumeRef.current)

        pianoRef.current = piano
        pianoVolumeRef.current = pianoVolume
        pianoReverbRef.current = pianoReverb
        pianoChorusRef.current = pianoChorus

        // Start the chorus effect
        pianoChorus.start()
      }

      setIsInitialized(true)
    }

    initAudio()

    return () => {
      // Clean up all oscillators
      oscillatorsRef.current.forEach((osc) => {
        if (osc.state === "started") {
          osc.stop()
        }
        osc.dispose()
      })
      spatialPannersRef.current.forEach((panner) => panner.dispose())
      spatialPannersRef.current = []

      if (brownianNoiseRef.current) {
        brownianNoiseRef.current.stop()
        brownianNoiseRef.current.dispose()
        brownianNoiseRef.current = null
      }
      if (brownianPannerRef.current) {
        brownianPannerRef.current.dispose()
        brownianPannerRef.current = null
      }
      if (brownianVolumeRef.current) {
        brownianVolumeRef.current.dispose()
        brownianVolumeRef.current = null
      }

      // Clean up master effects
      if (masterLimiterRef.current) {
        masterLimiterRef.current.dispose()
        masterLimiterRef.current = null
      }
      if (masterVolumeRef.current) {
        masterVolumeRef.current.dispose()
        masterVolumeRef.current = null
      }

      // Add piano cleanup in the cleanup return function
      if (pianoTimeoutRef.current) {
        clearTimeout(pianoTimeoutRef.current)
      }
      if (pianoRef.current) {
        pianoRef.current.dispose()
        pianoRef.current = null
      }
      if (pianoReverbRef.current) {
        pianoReverbRef.current.dispose()
        pianoReverbRef.current = null
      }
      if (pianoChorusRef.current) {
        pianoChorusRef.current.dispose()
        pianoChorusRef.current = null
      }
      if (pianoVolumeRef.current) {
        pianoVolumeRef.current.dispose()
        pianoVolumeRef.current = null
      }
    }
  }, [])

  // Update the main audio playback section in the useEffect
  useEffect(() => {
    if (!isInitialized || mode === "normal" || !masterVolumeRef.current) {
      // Stop all current oscillators with gentle fade
      oscillatorsRef.current.forEach((osc) => {
        if (osc.state === "started") {
          osc.volume.rampTo(-60, 3)
          setTimeout(() => {
            osc.stop()
            osc.dispose()
          }, 3000)
        }
      })
      oscillatorsRef.current = []
      spatialPannersRef.current.forEach((panner) => panner.dispose())
      spatialPannersRef.current = []
      onUpdateActivePositions(new Set())
      return
    }

    const playFrequencySequence = () => {
      const currentFreq = frequencies[currentIndexRef.current]
      const activePositions = new Set<string>()

      if (isLayered) {
        // Calculate total number of oscillators for proper volume distribution
        const totalOscillators = spatialLayers.reduce((sum, layer) => sum + layer.positions.length, 0)

        // Create spatial oscillators for all layers
        spatialLayers.forEach((layer, layerIndex) => {
          layer.positions.forEach((position, posIndex) => {
            const oscillator = new Tone.Oscillator({
              frequency: currentFreq.freq + posIndex * 1.2,
              type: "sine",
              volume: -30, // Increased from -50dB to -30dB for audible levels
            })

            const panner = new Tone.Panner3D({
              positionX: position.x,
              positionY: position.y,
              positionZ: position.z,
              orientationX: 0,
              orientationY: 0,
              orientationZ: -1,
            })

            const volume = new Tone.Volume(-30)

            // Connect through master volume/limiter chain
            oscillator.chain(volume, panner, masterVolumeRef.current!)

            // Calculate audible volume level based on layer percentage
            const baseVolume = -20 // Much higher base volume for audibility
            const layerVolumeMultiplier = layer.volume / 100
            const oscillatorReduction = Math.log10(totalOscillators) * 6 // Reduced reduction
            const finalVolume = baseVolume + layerVolumeMultiplier * 20 - oscillatorReduction

            setTimeout(() => {
              oscillator.start()
              volume.volume.rampTo(Math.max(-40, finalVolume), 5) // Higher minimum volume
              activePositions.add(`${layer.id}-${posIndex}`)
            }, posIndex * 200)

            oscillatorsRef.current.push(oscillator)
            spatialPannersRef.current.push(panner)

            setTimeout(
              () => {
                if (oscillator.state === "started") {
                  volume.volume.rampTo(-60, 5)
                  setTimeout(() => {
                    oscillator.stop()
                    oscillator.dispose()
                    panner.dispose()
                  }, 5000)
                }
              },
              (currentFreq.duration - 5) * 1000,
            )
          })
        })
      } else {
        // Single stereo oscillator
        const oscillator = new Tone.Oscillator({
          frequency: currentFreq.freq,
          type: "sine",
          volume: -20, // Increased from -35dB
        })

        const volume = new Tone.Volume(-20)
        oscillator.chain(volume, masterVolumeRef.current!)

        oscillator.start()
        volume.volume.rampTo(-10, 5) // Much higher volume for audibility

        oscillatorsRef.current.push(oscillator)

        setTimeout(
          () => {
            if (oscillator.state === "started") {
              volume.volume.rampTo(-60, 5)
              setTimeout(() => {
                oscillator.stop()
                oscillator.dispose()
              }, 5000)
            }
          },
          (currentFreq.duration - 5) * 1000,
        )
      }

      onUpdateActivePositions(activePositions)

      setTimeout(() => {
        currentIndexRef.current = (currentIndexRef.current + 1) % frequencies.length
        oscillatorsRef.current = oscillatorsRef.current.filter((osc) => osc.state === "started")
        spatialPannersRef.current = spatialPannersRef.current.filter((panner) => !panner.disposed)
        playFrequencySequence()
      }, currentFreq.duration * 1000)
    }

    playFrequencySequence()
  }, [mode, isLayered, isInitialized, frequencies, spatialLayers])

  // Update the brownian noise section to also use the master chain and respond to volume changes
  useEffect(() => {
    if (!isInitialized || !masterVolumeRef.current) return

    if (isBrownianActive && !brownianNoiseRef.current) {
      const brownianNoise = new Tone.Noise({
        type: "brown",
        volume: -25, // Increased from -50dB to -25dB for audibility
      })

      const volume = new Tone.Volume(-25)
      const panner = new Tone.Panner3D({
        positionX: 0,
        positionY: 0,
        positionZ: 2,
        orientationX: 0,
        orientationY: 0,
        orientationZ: -1,
      })

      // Connect through master volume/limiter chain
      brownianNoise.chain(volume, panner, masterVolumeRef.current)

      brownianNoiseRef.current = brownianNoise
      brownianVolumeRef.current = volume
      brownianPannerRef.current = panner

      brownianNoise.start()

      // Calculate volume based on brownian layer percentage - much more audible
      const brownianVolumeDb = -25 + (brownianLayer.volume / 100) * 30 // Increased range
      volume.volume.rampTo(brownianVolumeDb, 3)

      const animateSpatialMovement = () => {
        if (brownianPannerRef.current) {
          brownianPannerRef.current.positionZ.rampTo(-3, 20)
          setTimeout(() => {
            if (brownianPannerRef.current) {
              brownianPannerRef.current.positionZ.rampTo(2, 20)
              setTimeout(animateSpatialMovement, 20000)
            }
          }, 20000)
        }
      }

      animateSpatialMovement()
    } else if (!isBrownianActive && brownianNoiseRef.current) {
      if (brownianVolumeRef.current) {
        brownianVolumeRef.current.volume.rampTo(-60, 3)
      }

      setTimeout(() => {
        if (brownianNoiseRef.current) {
          brownianNoiseRef.current.stop()
          brownianNoiseRef.current.dispose()
          brownianNoiseRef.current = null
        }
        if (brownianPannerRef.current) {
          brownianPannerRef.current.dispose()
          brownianPannerRef.current = null
        }
        if (brownianVolumeRef.current) {
          brownianVolumeRef.current.dispose()
          brownianVolumeRef.current = null
        }
      }, 3000)
    }
  }, [isBrownianActive, isInitialized])

  // Update brownian noise volume when brownianLayer.volume changes
  useEffect(() => {
    if (brownianVolumeRef.current && brownianNoiseRef.current && isBrownianActive) {
      const brownianVolumeDb = -25 + (brownianLayer.volume / 100) * 30 // Increased range for audibility
      brownianVolumeRef.current.volume.rampTo(brownianVolumeDb, 1) // Quick volume adjustment
    }
  }, [brownianLayer.volume, isBrownianActive])

  // Add this new useEffect for piano system after the brownian noise useEffect
  // Piano chord progression system
  useEffect(() => {
    if (!isInitialized || !isPianoActive || !pianoRef.current || !masterVolumeRef.current) {
      // Stop piano if inactive
      if (pianoTimeoutRef.current) {
        clearTimeout(pianoTimeoutRef.current)
        pianoTimeoutRef.current = null
      }
      return
    }

    const progression = CHORD_PROGRESSIONS[pianoProgression as keyof typeof CHORD_PROGRESSIONS]
    if (!progression) return

    const playChordProgression = () => {
      const piano = pianoRef.current!
      const pianoVolume = pianoVolumeRef.current!

      let currentChord = 0
      const beatDuration = (60 / progression.bpm) * 1000 // Convert BPM to milliseconds per beat

      const playNextChord = () => {
        if (!isPianoActive || !pianoRef.current) return

        const chord = progression.chords[currentChord]

        if (!chord.pause && chord.notes.length > 0) {
          // Gentle volume fade in
          pianoVolume.volume.rampTo(-8, 0.5)

          // Play chord with gentle attack
          piano.triggerAttackRelease(chord.notes, chord.duration * (60 / progression.bpm))

          // Gentle fade out
          setTimeout(
            () => {
              if (pianoVolumeRef.current) {
                pianoVolume.volume.rampTo(-20, 1)
              }
            },
            chord.duration * (60 / progression.bpm) * 1000 - 1000,
          )
        }

        currentChord = (currentChord + 1) % progression.chords.length

        pianoTimeoutRef.current = setTimeout(playNextChord, chord.duration * beatDuration)
      }

      playNextChord()
    }

    playChordProgression()

    return () => {
      if (pianoTimeoutRef.current) {
        clearTimeout(pianoTimeoutRef.current)
        pianoTimeoutRef.current = null
      }
    }
  }, [isPianoActive, pianoProgression, isInitialized])

  return null
}

// Tooltip component
function Tooltip({ children, content }: { children: React.ReactNode; content: string }) {
  const [isVisible, setIsVisible] = useState(false)

  return (
    <div className="relative" onMouseEnter={() => setIsVisible(true)} onMouseLeave={() => setIsVisible(false)}>
      {children}
      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.9 }}
            className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 px-3 py-2 bg-black/90 backdrop-blur-sm text-white text-sm rounded-lg border border-white/20 whitespace-pre-line z-50 max-w-xs"
          >
            {content}
            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-black/90" />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default function Component() {
  const [mode, setMode] = useState("normal")
  const [isLayered, setIsLayered] = useState(false)
  const [showControls, setShowControls] = useState(true)
  const [mousePosition, setMousePosition] = useState(new THREE.Vector2(0, 0))
  const [isBrownianActive, setIsBrownianActive] = useState(false)
  const [showSpatialViz, setShowSpatialViz] = useState(false)
  const [activePositions, setActivePositions] = useState<Set<string>>(new Set())
  const [audioLevels, setAudioLevels] = useState<AudioLevels>({
    layer1: -60,
    layer2: -60,
    layer3: -60,
    brownian: -60,
    master: -60,
  })
  const [spatialLayers, setSpatialLayers] = useState<SpatialLayer[]>([
    {
      id: 1,
      distance: 0.5, // 2 inches from center (0.5 * 4)
      volume: 30,
      positions: [
        { x: 0, y: 0, z: 0.5, angle: 0, label: "Front", isActive: false },
        { x: -0.35, y: 0, z: 0, angle: 270, label: "Left", isActive: false },
        { x: 0.35, y: 0, z: 0, angle: 90, label: "Right", isActive: false },
        { x: 0, y: 0, z: -0.5, angle: 180, label: "Behind", isActive: false },
        { x: 0, y: 0, z: -1.0, angle: 180, label: "Far Behind", isActive: false },
      ],
    },
    {
      id: 2,
      distance: 1.25, // 5 inches from center (1.25 * 4)
      volume: 30,
      positions: [
        { x: 0, y: 0, z: 1.25, angle: 0, label: "Front", isActive: false },
        { x: -0.88, y: 0, z: 0, angle: 270, label: "Left", isActive: false },
        { x: 0.88, y: 0, z: 0, angle: 90, label: "Right", isActive: false },
        { x: 0, y: 0, z: -1.25, angle: 180, label: "Behind", isActive: false },
        { x: 0, y: 0, z: -2.5, angle: 180, label: "Far Behind", isActive: false },
      ],
    },
    {
      id: 3,
      distance: 2.25, // 9 inches from center (2.25 * 4)
      volume: 30,
      positions: [
        { x: 0, y: 0, z: 2.25, angle: 0, label: "Front", isActive: false },
        { x: -1.59, y: 0, z: 0, angle: 270, label: "Left", isActive: false },
        { x: 1.59, y: 0, z: 0, angle: 90, label: "Right", isActive: false },
        { x: 0, y: 0, z: -2.25, angle: 180, label: "Behind", isActive: false },
        { x: 0, y: 0, z: -4.5, angle: 180, label: "Far Behind", isActive: false },
      ],
    },
  ])
  const [brownianLayer, setBrownianLayer] = useState<BrownianLayer>({
    id: 4,
    volume: 10,
    isActive: false,
  })
  const [isClient, setIsClient] = useState(false)

  // Add piano progression state after the existing state declarations
  const [pianoProgression, setPianoProgression] = useState<string>("gentleWorship")
  const [isPianoActive, setIsPianoActive] = useState(false)

  // Add interactive audio handler state
  const [interactiveHandler, setInteractiveHandler] = useState<InteractiveAudioHandler | null>(null)
  const [interactionState, setInteractionState] = useState({
    isActive: false,
    intensity: 0,
    frequency: 0,
    vowel: "a",
    activeLayerCount: 0,
  })

  useEffect(() => {
    setIsClient(true)
  }, [])

  // Initialize interactive handler when audio system is ready
  useEffect(() => {
    if (isClient && !interactiveHandler) {
      // We'll initialize this after the audio system is ready
      const handler = new InteractiveAudioHandler(null)
      setInteractiveHandler(handler)

      return () => {
        handler.dispose()
      }
    }
  }, [isClient, interactiveHandler])

  // Update interactive handler with master volume reference
  useEffect(() => {
    if (interactiveHandler) {
      // This will be set when AudioSystem initializes
      const updateHandler = () => {
        const state = interactiveHandler.getInteractionState()
        setInteractionState(state)
      }

      const interval = setInterval(updateHandler, 50) // Update 20 times per second
      return () => clearInterval(interval)
    }
  }, [interactiveHandler])

  // Update brownian layer active state when toggle changes
  useEffect(() => {
    setBrownianLayer((prev) => ({ ...prev, isActive: isBrownianActive }))
  }, [isBrownianActive])

  if (!isClient) {
    return (
      <div className="w-full h-screen flex items-center justify-center bg-gradient-radial from-blue-900/20 to-black">
        <div className="text-white text-lg">Loading Healing Frequencies...</div>
      </div>
    )
  }

  const handleMouseMove = (event: React.MouseEvent) => {
    const rect = event.currentTarget.getBoundingClientRect()
    const x = ((event.clientX - rect.left) / rect.width) * 2 - 1
    const y = (-(event.clientY - rect.top) / rect.height) * 2 + 1
    setMousePosition(new THREE.Vector2(x, y))

    // Update interactive handler
    if (interactiveHandler) {
      interactiveHandler.onMouseMove(x, y)
    }
  }

  const handleMouseDown = (event: React.MouseEvent) => {
    const rect = event.currentTarget.getBoundingClientRect()
    const x = ((event.clientX - rect.left) / rect.width) * 2 - 1
    const y = (-(event.clientY - rect.top) / rect.height) * 2 + 1

    if (interactiveHandler) {
      interactiveHandler.onMouseDown(x, y)
    }
  }

  const handleMouseUp = () => {
    if (interactiveHandler) {
      interactiveHandler.onMouseUp()
    }
  }

  const handleModeChange = async (newMode: string) => {
    if (Tone.context.state !== "running") {
      await Tone.start()
    }
    setMode(newMode)
    setShowControls(false)
  }

  const updateSpatialLayer = (layerId: number, updates: Partial<SpatialLayer>) => {
    setSpatialLayers((prev) =>
      prev.map((layer) => {
        if (layer.id === layerId) {
          const updatedLayer = { ...layer, ...updates }
          // Recalculate positions based on new distance
          if (updates.distance !== undefined) {
            updatedLayer.positions = updatedLayer.positions.map((pos, index) => {
              const angles = [0, 270, 90, 180, 180]
              const distances = [1, 0.7, 0.7, 1, 2]
              const angle = angles[index]
              const distanceMultiplier = distances[index]
              return {
                ...pos,
                x: Math.cos((angle * Math.PI) / 180) * updatedLayer.distance * distanceMultiplier,
                y: 0,
                z: Math.sin((angle * Math.PI) / 180) * updatedLayer.distance * distanceMultiplier,
                angle,
              }
            })
          }
          return updatedLayer
        }
        return layer
      }),
    )
  }

  const updateBrownianLayer = (updates: Partial<BrownianLayer>) => {
    setBrownianLayer((prev) => ({ ...prev, ...updates }))
  }

  // Tooltip content for each mode
  const getTooltipContent = (modeKey: string) => {
    const frequencies = FREQUENCIES[modeKey as keyof typeof FREQUENCIES]
    if (!frequencies) return ""

    return frequencies.map((freq, index) => `${index + 1}. ${freq.label}`).join("\n")
  }

  const layeredTooltipContent = `Creates immersive 3D spatial audio experience:
• 3 concentric layers with 5 positions each (15 total)
• Layer distances: 2", 5", 9" from center
• Volume distribution adjustable (should total 100%)
• HRTF processing for realistic spatial perception
• Real-time dB monitoring and adjustable volumes`

  const brownianTooltipContent = "Brownian Noise Layers for soothing effect"

  const voiceTooltipContent = `Interactive Ethereal Voice Synthesis:
• Click and drag to sing harmonic notes
• 5-layer voice synthesis with formant filtering
• Real-time vowel morphing (Y-axis: u→o→a→e→i)
• Cathedral reverb and ping-pong delay
• Intensity builds with hold duration
• Breathiness and vibrato for realism`

  return (
    <div
      className="w-full h-screen relative overflow-hidden cursor-crosshair"
      onMouseMove={handleMouseMove}
      onMouseDown={handleMouseDown}
      onMouseUp={handleMouseUp}
      onMouseEnter={() => setShowControls(true)}
    >
      {/* Glowing border */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute inset-0 border-4 border-cyan-400/30 shadow-[0_0_20px_rgba(34,211,238,0.3)]" />
        <div className="absolute inset-2 border-2 border-white/20 shadow-[0_0_10px_rgba(255,255,255,0.2)]" />
      </div>

      {/* Interactive Instructions */}
      {!interactionState.isActive && (
        <div className="absolute bottom-6 left-6 z-10 pointer-events-none">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 0.7, y: 0 }}
            className="bg-black/60 backdrop-blur-sm rounded-lg p-3 text-white/80 text-sm"
          >
            <div className="flex items-center space-x-2">
              <span>🎵</span>
              <span>Click and drag to create ethereal voice harmonies</span>
            </div>
          </motion.div>
        </div>
      )}

      {/* Voice Interaction State Display */}
      {interactionState.isActive && (
        <div className="absolute bottom-6 left-6 z-10 pointer-events-none">
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-black/80 backdrop-blur-sm rounded-lg p-4 text-white border border-cyan-400/30"
          >
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <span className="text-cyan-400">♪</span>
                <span className="text-sm">{interactionState.frequency.toFixed(1)} Hz</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-green-400">⚡</span>
                <div className="flex-1 bg-gray-700 rounded-full h-2">
                  <div
                    className="h-full bg-gradient-to-r from-green-400 to-cyan-400 rounded-full transition-all duration-200"
                    style={{ width: `${interactionState.intensity * 100}%` }}
                  />
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-purple-400">🎤</span>
                <span className="text-sm">
                  Vowel: {interactionState.vowel.toUpperCase()} | Layers: {interactionState.activeLayerCount}
                </span>
              </div>
            </div>
          </motion.div>
        </div>
      )}

      {/* Controls */}
      <div
        className="absolute top-0 left-0 right-0 z-10 p-6 pointer-events-auto"
        onMouseEnter={() => setShowControls(true)}
      >
        <AnimatePresence>
          {showControls && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="flex flex-col items-center space-y-4"
            >
              {/* Mode Selection */}
              <div className="flex justify-center space-x-4">
                {[
                  { key: "normal", label: "Normal" },
                  { key: "pleasure", label: "Pleasure" },
                  { key: "antiPain", label: "Anti Pain Extreme" },
                ].map(({ key, label }) => (
                  <Tooltip key={key} content={getTooltipContent(key)}>
                    <motion.button
                      onClick={() => handleModeChange(key)}
                      className={`px-6 py-3 rounded-full backdrop-blur-sm border transition-all ${
                        mode === key
                          ? "bg-cyan-400/20 border-cyan-400/50 text-cyan-100"
                          : "bg-white/10 border-white/20 text-white/80 hover:bg-white/20"
                      }`}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      {label}
                    </motion.button>
                  </Tooltip>
                ))}
                {/* Brownian Noise Toggle */}
                <Tooltip content={brownianTooltipContent}>
                  <motion.button
                    onClick={() => setIsBrownianActive(!isBrownianActive)}
                    className={`px-3 py-3 rounded-full backdrop-blur-sm border transition-all ${
                      isBrownianActive
                        ? "bg-amber-400/20 border-amber-400/50 text-amber-100"
                        : "bg-white/10 border-white/20 text-white/80 hover:bg-white/20"
                    }`}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    🌊
                  </motion.button>
                </Tooltip>

                {/* Piano Progression Dropdown */}
                <div className="relative">
                  <Tooltip content="Select gentle piano chord progression">
                    <motion.button
                      onClick={() => setIsPianoActive(!isPianoActive)}
                      className={`px-3 py-3 rounded-full backdrop-blur-sm border transition-all ${
                        isPianoActive
                          ? "bg-green-400/20 border-green-400/50 text-green-100"
                          : "bg-white/10 border-white/20 text-white/80 hover:bg-white/20"
                      }`}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      🎹
                    </motion.button>
                  </Tooltip>

                  {isPianoActive && (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="absolute top-full left-0 mt-2 bg-black/90 backdrop-blur-sm border border-white/20 rounded-lg overflow-hidden z-20"
                    >
                      {Object.entries(CHORD_PROGRESSIONS).map(([key, progression]) => (
                        <button
                          key={key}
                          onClick={() => setPianoProgression(key)}
                          className={`block w-full px-4 py-2 text-left text-sm transition-colors ${
                            pianoProgression === key
                              ? "bg-green-400/20 text-green-100"
                              : "text-white/80 hover:bg-white/10"
                          }`}
                        >
                          {progression.name}
                        </button>
                      ))}
                    </motion.div>
                  )}
                </div>

                {/* Voice Synthesis Indicator */}
                <Tooltip content={voiceTooltipContent}>
                  <motion.div
                    className={`px-3 py-3 rounded-full backdrop-blur-sm border transition-all ${
                      interactionState.isActive
                        ? "bg-purple-400/20 border-purple-400/50 text-purple-100"
                        : "bg-white/10 border-white/20 text-white/80"
                    }`}
                  >
                    🎤
                  </motion.div>
                </Tooltip>
              </div>

              {/* Layered Toggle */}
              {mode !== "normal" && (
                <div className="flex items-center space-x-2">
                  <Tooltip content={layeredTooltipContent}>
                    <motion.button
                      onClick={() => setIsLayered(!isLayered)}
                      className={`px-6 py-2 rounded-full backdrop-blur-sm border transition-all ${
                        isLayered
                          ? "bg-purple-400/20 border-purple-400/50 text-purple-100"
                          : "bg-white/10 border-white/20 text-white/80 hover:bg-white/20"
                      }`}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.8 }}
                    >
                      {isLayered ? "🎧 Layered 3D" : "🔊 Stereo"}
                    </motion.button>
                  </Tooltip>

                  {/* Spatial Visualization Toggle */}
                  {isLayered && (
                    <motion.button
                      onClick={() => setShowSpatialViz(!showSpatialViz)}
                      className={`px-3 py-2 rounded-full backdrop-blur-sm border transition-all ${
                        showSpatialViz
                          ? "bg-green-400/20 border-green-400/50 text-green-100"
                          : "bg-white/10 border-white/20 text-white/80 hover:bg-white/20"
                      }`}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.8 }}
                    >
                      📊
                    </motion.button>
                  )}
                </div>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Spatial Visualization Window */}
      <SpatialVisualization
        isVisible={showSpatialViz}
        onToggle={() => setShowSpatialViz(false)}
        layers={spatialLayers}
        brownianLayer={brownianLayer}
        onUpdateLayer={updateSpatialLayer}
        onUpdateBrownianLayer={updateBrownianLayer}
        activePositions={activePositions}
        audioLevels={audioLevels}
      />

      {/* 3D Scene */}
      <Canvas
        camera={{ position: [0, 0, 5], fov: 75 }}
        style={{ background: "radial-gradient(circle, #001122 0%, #000000 100%)" }}
      >
        <Environment preset="night" />
        <ambientLight intensity={0.2} />

        <PulsingCenter />
        <HealingRings mode={mode} mousePosition={mousePosition} interactionState={interactionState} />
      </Canvas>

      <AudioSystem
        mode={mode}
        isLayered={isLayered}
        isBrownianActive={isBrownianActive}
        isPianoActive={isPianoActive}
        pianoProgression={pianoProgression}
        spatialLayers={spatialLayers}
        brownianLayer={brownianLayer}
        onUpdateActivePositions={setActivePositions}
        onUpdateAudioLevels={setAudioLevels}
        interactiveHandler={interactiveHandler}
      />
    </div>
  )
}
