import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ay<PERSON> } from "../../constants/types"

/**
 * Create default spatial layers configuration
 */
export function createDefaultSpatialLayers(): SpatialLayer[] {
  return [
    {
      id: 1,
      distance: 0.5, // 2 inches from center (0.5 * 4)
      volume: 30,
      positions: [
        { x: 0, y: 0, z: 0.5, angle: 0, label: "Front", isActive: false },
        { x: -0.35, y: 0, z: 0, angle: 270, label: "Left", isActive: false },
        { x: 0.35, y: 0, z: 0, angle: 90, label: "Right", isActive: false },
        { x: 0, y: 0, z: -0.5, angle: 180, label: "Behind", isActive: false },
        { x: 0, y: 0, z: -1.0, angle: 180, label: "Far Behind", isActive: false },
      ],
    },
    {
      id: 2,
      distance: 1.25, // 5 inches from center (1.25 * 4)
      volume: 30,
      positions: [
        { x: 0, y: 0, z: 1.25, angle: 0, label: "Front", isActive: false },
        { x: -0.88, y: 0, z: 0, angle: 270, label: "Left", isActive: false },
        { x: 0.88, y: 0, z: 0, angle: 90, label: "Right", isActive: false },
        { x: 0, y: 0, z: -1.25, angle: 180, label: "Behind", isActive: false },
        { x: 0, y: 0, z: -2.5, angle: 180, label: "Far Behind", isActive: false },
      ],
    },
    {
      id: 3,
      distance: 2.25, // 9 inches from center (2.25 * 4)
      volume: 30,
      positions: [
        { x: 0, y: 0, z: 2.25, angle: 0, label: "Front", isActive: false },
        { x: -1.59, y: 0, z: 0, angle: 270, label: "Left", isActive: false },
        { x: 1.59, y: 0, z: 0, angle: 90, label: "Right", isActive: false },
        { x: 0, y: 0, z: -2.25, angle: 180, label: "Behind", isActive: false },
        { x: 0, y: 0, z: -4.5, angle: 180, label: "Far Behind", isActive: false },
      ],
    },
  ]
}

/**
 * Create default brownian layer configuration
 */
export function createDefaultBrownianLayer(): BrownianLayer {
  return {
    id: 4,
    volume: 10,
    isActive: false,
  }
}

/**
 * Update spatial layer with new distance and recalculate positions
 */
export function updateSpatialLayerDistance(layer: SpatialLayer, newDistance: number): SpatialLayer {
  const updatedLayer = { ...layer, distance: newDistance }
  
  // Recalculate positions based on new distance
  updatedLayer.positions = updatedLayer.positions.map((pos, index) => {
    const angles = [0, 270, 90, 180, 180]
    const distances = [1, 0.7, 0.7, 1, 2]
    const angle = angles[index]
    const distanceMultiplier = distances[index]
    
    return {
      ...pos,
      x: Math.cos((angle * Math.PI) / 180) * updatedLayer.distance * distanceMultiplier,
      y: 0,
      z: Math.sin((angle * Math.PI) / 180) * updatedLayer.distance * distanceMultiplier,
      angle,
    }
  })
  
  return updatedLayer
}

/**
 * Update spatial layer volume
 */
export function updateSpatialLayerVolume(layer: SpatialLayer, newVolume: number): SpatialLayer {
  return {
    ...layer,
    volume: Math.max(0, Math.min(100, newVolume))
  }
}

/**
 * Update brownian layer volume
 */
export function updateBrownianLayerVolume(layer: BrownianLayer, newVolume: number): BrownianLayer {
  return {
    ...layer,
    volume: Math.max(0, Math.min(100, newVolume))
  }
}

/**
 * Calculate total volume across all layers
 */
export function calculateTotalVolume(spatialLayers: SpatialLayer[], brownianLayer: BrownianLayer): number {
  const spatialVolume = spatialLayers.reduce((sum, layer) => sum + layer.volume, 0)
  return spatialVolume + brownianLayer.volume
}

/**
 * Check if volume distribution is balanced (should total 100%)
 */
export function isVolumeBalanced(spatialLayers: SpatialLayer[], brownianLayer: BrownianLayer): boolean {
  const totalVolume = calculateTotalVolume(spatialLayers, brownianLayer)
  return Math.abs(totalVolume - 100) <= 0.1
}

/**
 * Get total number of oscillators across all spatial layers
 */
export function getTotalOscillatorCount(spatialLayers: SpatialLayer[]): number {
  return spatialLayers.reduce((sum, layer) => sum + layer.positions.length, 0)
}
