"use client"

import { motion } from "framer-motion"

interface InteractionDisplayProps {
  interactionState: {
    isActive: boolean
    intensity: number
    frequency: number
    vowel: string
    activeLayerCount: number
  }
}

export function InteractionDisplay({ interactionState }: InteractionDisplayProps) {
  return (
    <div className="absolute bottom-6 left-6 z-10 pointer-events-none">
      {!interactionState.isActive ? (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 0.7, y: 0 }}
          className="bg-black/60 backdrop-blur-sm rounded-lg p-3 text-white/80 text-sm"
        >
          <div className="flex items-center space-x-2">
            <span>🎵</span>
            <span>Click and drag to create ethereal voice harmonies</span>
          </div>
        </motion.div>
      ) : (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-black/80 backdrop-blur-sm rounded-lg p-4 text-white border border-cyan-400/30"
        >
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <span className="text-cyan-400">♪</span>
              <span className="text-sm">{interactionState.frequency.toFixed(1)} Hz</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-green-400">⚡</span>
              <div className="flex-1 bg-gray-700 rounded-full h-2">
                <div
                  className="h-full bg-gradient-to-r from-green-400 to-cyan-400 rounded-full transition-all duration-200"
                  style={{ width: `${interactionState.intensity * 100}%` }}
                />
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-purple-400">🎤</span>
              <span className="text-sm">
                Vowel: {interactionState.vowel.toUpperCase()} | Layers: {interactionState.activeLayerCount}
              </span>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  )
}
