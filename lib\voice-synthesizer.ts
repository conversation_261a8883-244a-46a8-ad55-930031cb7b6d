import * as Tone from "tone"

// Formant frequencies for different vowel sounds (female voice)
const FORMANT_FREQUENCIES = {
  a: { f1: 850, f2: 1220, f3: 2810 }, // "ah" sound
  e: { f1: 400, f2: 2300, f3: 3100 }, // "eh" sound
  i: { f1: 270, f2: 2290, f3: 3010 }, // "ee" sound
  o: { f1: 450, f2: 880, f3: 2830 }, // "oh" sound
  u: { f1: 325, f2: 700, f3: 2530 }, // "oo" sound
}

interface VoiceLayer {
  oscillator: Tone.Oscillator
  filter1: Tone.Filter
  filter2: Tone.Filter
  filter3: Tone.Filter
  volume: Tone.Volume
  chorus: Tone.Chorus
  vibrato: Tone.Vibrato
  tremolo: Tone.Tremolo
  isActive: boolean
}

export class VoiceSynthesizer {
  private masterVolume: Tone.Volume | null = null
  private voiceLayers: VoiceLayer[] = []
  private reverb: Tone.Reverb | null = null
  private delay: Tone.PingPongDelay | null = null
  private compressor: Tone.Compressor | null = null
  private currentFrequency = 440
  private currentVowel: keyof typeof FORMANT_FREQUENCIES = "a"
  private isActive = false
  private intensity = 0
  private breathiness: Tone.Noise | null = null
  private breathinessFilter: Tone.Filter | null = null
  private breathinessVolume: Tone.Volume | null = null

  constructor(masterVolume: Tone.Volume | null) {
    this.masterVolume = masterVolume
    this.initializeVoice()
  }

  private async initializeVoice() {
    if (!this.masterVolume) return

    // Create reverb for cathedral-like sound
    this.reverb = new Tone.Reverb({
      roomSize: 0.9,
      dampening: 800,
      wet: 0.6,
    })

    // Create ping-pong delay for ethereal echo
    this.delay = new Tone.PingPongDelay({
      delayTime: "8n",
      feedback: 0.3,
      wet: 0.2,
    })

    // Create compressor for smooth dynamics
    this.compressor = new Tone.Compressor({
      threshold: -24,
      ratio: 4,
      attack: 0.1,
      release: 0.3,
    })

    // Create breathiness layer (subtle noise for realism)
    this.breathiness = new Tone.Noise({
      type: "pink",
      volume: -45,
    })

    this.breathinessFilter = new Tone.Filter({
      frequency: 2000,
      type: "lowpass",
      rolloff: -24,
    })

    this.breathinessVolume = new Tone.Volume(-40)

    // Chain breathiness
    this.breathiness.chain(this.breathinessFilter, this.breathinessVolume, this.reverb)

    // Create 5 harmonic voice layers
    for (let i = 0; i < 5; i++) {
      const layer = await this.createVoiceLayer(i)
      this.voiceLayers.push(layer)
    }

    // Chain effects to master
    this.reverb.chain(this.delay, this.compressor, this.masterVolume)
  }

  private async createVoiceLayer(layerIndex: number): Promise<VoiceLayer> {
    // Harmonic ratios for natural voice harmonics
    const harmonicRatios = [1.0, 2.0, 3.0, 4.0, 5.0]
    const harmonicRatio = harmonicRatios[layerIndex]

    // Create oscillator with slight detuning for chorus effect
    const detune = (layerIndex - 2) * 3 // -6 to +6 cents detuning
    const oscillator = new Tone.Oscillator({
      type: "sawtooth", // Rich harmonics like vocal cords
      volume: -30 - layerIndex * 3, // Decreasing volume for higher harmonics
      detune: detune,
    })

    // Create formant filters (simulating vocal tract resonances)
    const filter1 = new Tone.Filter({
      frequency: FORMANT_FREQUENCIES.a.f1,
      type: "bandpass",
      Q: 8,
      gain: 6,
    })

    const filter2 = new Tone.Filter({
      frequency: FORMANT_FREQUENCIES.a.f2,
      type: "bandpass",
      Q: 12,
      gain: 4,
    })

    const filter3 = new Tone.Filter({
      frequency: FORMANT_FREQUENCIES.a.f3,
      type: "bandpass",
      Q: 10,
      gain: 2,
    })

    // Volume control for this layer
    const volume = new Tone.Volume(-20 - layerIndex * 2)

    // Chorus for lush, choir-like effect
    const chorus = new Tone.Chorus({
      frequency: 0.5 + layerIndex * 0.1,
      delayTime: 2 + layerIndex * 0.5,
      depth: 0.3,
      wet: 0.4,
    })

    // Vibrato for human-like expression
    const vibrato = new Tone.Vibrato({
      frequency: 4.5 + layerIndex * 0.2,
      depth: 0.02,
      wet: 0.3,
    })

    // Tremolo for additional expression
    const tremolo = new Tone.Tremolo({
      frequency: 3 + layerIndex * 0.1,
      depth: 0.1,
      wet: 0.2,
    })

    // Chain the voice layer
    oscillator.chain(filter1, filter2, filter3, volume, vibrato, tremolo, chorus, this.reverb!)

    // Start effects
    chorus.start()
    vibrato.start()
    tremolo.start()

    return {
      oscillator,
      filter1,
      filter2,
      filter3,
      volume,
      chorus,
      vibrato,
      tremolo,
      isActive: false,
    }
  }

  // Start voice synthesis
  start(frequency: number, x: number, y: number) {
    if (this.isActive || !this.masterVolume) return

    this.isActive = true
    this.currentFrequency = frequency
    this.intensity = 0

    // Map Y position to vowel sound
    const normalizedY = (y + 1) / 2
    const vowels: (keyof typeof FORMANT_FREQUENCIES)[] = ["u", "o", "a", "e", "i"]
    const vowelIndex = Math.floor(normalizedY * vowels.length)
    this.currentVowel = vowels[Math.min(vowelIndex, vowels.length - 1)]

    // Start breathiness
    if (this.breathiness) {
      this.breathiness.start()
    }

    // Start all voice layers
    this.voiceLayers.forEach((layer, index) => {
      if (!layer.isActive) {
        const harmonicRatio = [1.0, 2.0, 3.0, 4.0, 5.0][index]
        layer.oscillator.frequency.setValueAtTime(frequency * harmonicRatio, Tone.now())
        layer.oscillator.start()
        layer.isActive = true

        // Gentle fade in
        layer.volume.volume.rampTo(-15 - index * 2, 1.5)
      }
    })

    // Update formant frequencies for current vowel
    this.updateFormants()
  }

  // Update voice parameters during interaction
  update(frequency: number, x: number, y: number, intensity: number) {
    if (!this.isActive) return

    this.currentFrequency = frequency
    this.intensity = intensity

    // Map Y position to vowel sound with smooth transitions
    const normalizedY = (y + 1) / 2
    const vowels: (keyof typeof FORMANT_FREQUENCIES)[] = ["u", "o", "a", "e", "i"]
    const vowelIndex = Math.floor(normalizedY * vowels.length)
    const newVowel = vowels[Math.min(vowelIndex, vowels.length - 1)]

    if (newVowel !== this.currentVowel) {
      this.currentVowel = newVowel
      this.updateFormants()
    }

    // Update frequency for all layers
    this.voiceLayers.forEach((layer, index) => {
      if (layer.isActive) {
        const harmonicRatio = [1.0, 2.0, 3.0, 4.0, 5.0][index]
        layer.oscillator.frequency.rampTo(frequency * harmonicRatio, 0.1)

        // Increase volume with intensity
        const targetVolume = -15 - index * 2 + intensity * 8
        layer.volume.volume.rampTo(targetVolume, 0.2)

        // Increase vibrato with intensity
        const vibratoDepth = 0.02 + intensity * 0.03
        layer.vibrato.depth.rampTo(vibratoDepth, 0.5)

        // Increase tremolo with intensity
        const tremoloDepth = 0.1 + intensity * 0.15
        layer.tremolo.depth.rampTo(tremoloDepth, 0.5)
      }
    })

    // Update breathiness with intensity
    if (this.breathinessVolume) {
      const breathinessLevel = -40 + intensity * 15
      this.breathinessVolume.volume.rampTo(breathinessLevel, 0.3)
    }

    // Map X position to chorus depth for spatial effect
    const normalizedX = (x + 1) / 2
    this.voiceLayers.forEach((layer) => {
      if (layer.isActive) {
        const chorusWet = 0.4 + normalizedX * 0.3
        layer.chorus.wet.rampTo(chorusWet, 0.5)
      }
    })
  }

  // Update formant frequencies for vowel morphing
  private updateFormants() {
    const formants = FORMANT_FREQUENCIES[this.currentVowel]

    this.voiceLayers.forEach((layer) => {
      if (layer.isActive) {
        // Smooth transition to new formant frequencies
        layer.filter1.frequency.rampTo(formants.f1, 0.3)
        layer.filter2.frequency.rampTo(formants.f2, 0.3)
        layer.filter3.frequency.rampTo(formants.f3, 0.3)
      }
    })
  }

  // Stop voice synthesis
  stop() {
    if (!this.isActive) return

    this.isActive = false

    // Fade out all layers
    this.voiceLayers.forEach((layer, index) => {
      if (layer.isActive) {
        const fadeTime = 2 + this.intensity * 2 // Longer fade for higher intensity
        layer.volume.volume.rampTo(-60, fadeTime)

        setTimeout(() => {
          if (layer.isActive) {
            layer.oscillator.stop()
            layer.isActive = false
          }
        }, fadeTime * 1000)
      }
    })

    // Fade out breathiness
    if (this.breathinessVolume && this.breathiness) {
      this.breathinessVolume.volume.rampTo(-60, 2)
      setTimeout(() => {
        if (this.breathiness) {
          this.breathiness.stop()
        }
      }, 2000)
    }

    this.intensity = 0
  }

  // Get current state
  getState() {
    return {
      isActive: this.isActive,
      frequency: this.currentFrequency,
      vowel: this.currentVowel,
      intensity: this.intensity,
      activeLayerCount: this.voiceLayers.filter((layer) => layer.isActive).length,
    }
  }

  // Clean up resources
  dispose() {
    this.stop()

    this.voiceLayers.forEach((layer) => {
      layer.oscillator.dispose()
      layer.filter1.dispose()
      layer.filter2.dispose()
      layer.filter3.dispose()
      layer.volume.dispose()
      layer.chorus.dispose()
      layer.vibrato.dispose()
      layer.tremolo.dispose()
    })

    if (this.reverb) this.reverb.dispose()
    if (this.delay) this.delay.dispose()
    if (this.compressor) this.compressor.dispose()
    if (this.breathiness) this.breathiness.dispose()
    if (this.breathinessFilter) this.breathinessFilter.dispose()
    if (this.breathinessVolume) this.breathinessVolume.dispose()

    this.voiceLayers = []
  }
}
