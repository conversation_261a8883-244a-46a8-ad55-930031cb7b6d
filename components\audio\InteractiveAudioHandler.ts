import type * as Tone from "tone"
import { VoiceSynthesizer } from "./VoiceSynthesizer"

// Musical scales and intervals for harmonic generation
const HARMONIC_SCALES = {
  // Pentatonic scales for healing frequencies
  healing: [0, 2, 4, 7, 9], // Major pentatonic intervals
  mystical: [0, 3, 5, 7, 10], // Minor pentatonic intervals
  ethereal: [0, 2, 5, 7, 9, 11], // Dorian mode intervals
  celestial: [0, 4, 7, 11, 14, 16], // Extended harmony intervals
}

export class InteractiveAudioHandler {
  private masterVolume: Tone.Volume | null = null
  private voiceSynthesizer: VoiceSynthesizer | null = null
  private isMouseDown = false
  private holdStartTime = 0
  private currentIntensity = 0
  private intensityInterval: NodeJS.Timeout | null = null
  private currentBaseFrequencies: number[] = []
  private currentMode = "normal"
  private isPianoActive = false
  private pianoProgression = "gentleWorship"

  constructor(masterVolume: Tone.Volume | null) {
    this.masterVolume = masterVolume
    this.initializeVoiceSynthesizer()
  }

  private async initializeVoiceSynthesizer() {
    if (this.masterVolume) {
      this.voiceSynthesizer = new VoiceSynthesizer(this.masterVolume)
    }
  }

  // Update current audio context
  updateAudioContext(baseFrequencies: number[], mode: string, isPianoActive: boolean, pianoProgression: string) {
    this.currentBaseFrequencies = baseFrequencies
    this.currentMode = mode
    this.isPianoActive = isPianoActive
    this.pianoProgression = pianoProgression
  }

  // Calculate harmonic frequency based on current audio context
  private calculateHarmonicFrequency(x: number, y: number): number {
    // Normalize coordinates to 0-1 range
    const normalizedX = (x + 1) / 2 // Convert from -1,1 to 0,1
    const normalizedY = (y + 1) / 2

    // Get base frequency from current context
    let baseFreq = 432 // Default to 432Hz if no frequencies playing

    if (this.currentBaseFrequencies.length > 0) {
      // Use the primary frequency being played
      baseFreq = this.currentBaseFrequencies[0]
    }

    // Select scale based on current mode and piano activity
    let scale = HARMONIC_SCALES.healing
    if (this.isPianoActive) {
      if (this.pianoProgression.includes("ephemeral") || this.pianoProgression.includes("time")) {
        scale = HARMONIC_SCALES.mystical
      } else if (this.pianoProgression.includes("river")) {
        scale = HARMONIC_SCALES.ethereal
      } else {
        scale = HARMONIC_SCALES.celestial
      }
    } else if (this.currentMode === "pleasure") {
      scale = HARMONIC_SCALES.ethereal
    } else if (this.currentMode === "antiPain") {
      scale = HARMONIC_SCALES.mystical
    }

    // Map Y position to octave (higher Y = higher octave) - optimized for female voice range
    const octaveMultiplier = Math.pow(2, Math.floor(normalizedY * 1.5) + 1) // 1-2.5 octaves up for female range

    // Map X position to scale degree
    const scaleIndex = Math.floor(normalizedX * scale.length)
    const semitoneOffset = scale[scaleIndex]

    // Calculate final frequency using equal temperament
    let frequency = baseFreq * octaveMultiplier * Math.pow(2, semitoneOffset / 12)

    // Clamp to comfortable female vocal range (200Hz - 1200Hz)
    frequency = Math.max(200, Math.min(1200, frequency))

    return frequency
  }

  // Handle mouse down event
  onMouseDown(x: number, y: number) {
    if (!this.masterVolume || this.isMouseDown || !this.voiceSynthesizer) return

    this.isMouseDown = true
    this.holdStartTime = Date.now()
    this.currentIntensity = 0

    // Calculate harmonic frequency
    const frequency = this.calculateHarmonicFrequency(x, y)

    // Start voice synthesis
    this.voiceSynthesizer.start(frequency, x, y)

    // Start intensity buildup
    this.startIntensityBuildup(x, y)
  }

  // Handle mouse move event (for pitch bending and vowel morphing)
  onMouseMove(x: number, y: number) {
    if (!this.isMouseDown || !this.voiceSynthesizer) return

    // Calculate new frequency for pitch bending
    const newFrequency = this.calculateHarmonicFrequency(x, y)

    // Update voice synthesis
    this.voiceSynthesizer.update(newFrequency, x, y, this.currentIntensity)
  }

  // Start intensity buildup over time
  private startIntensityBuildup(x: number, y: number) {
    this.intensityInterval = setInterval(() => {
      if (!this.isMouseDown || !this.voiceSynthesizer) {
        if (this.intensityInterval) {
          clearInterval(this.intensityInterval)
          this.intensityInterval = null
        }
        return
      }

      const holdDuration = Date.now() - this.holdStartTime
      const maxIntensity = 1.0
      const buildupTime = 6000 // 6 seconds to reach max intensity for voice

      this.currentIntensity = Math.min(maxIntensity, holdDuration / buildupTime)

      // Update voice with current intensity
      const frequency = this.calculateHarmonicFrequency(x, y)
      this.voiceSynthesizer.update(frequency, x, y, this.currentIntensity)
    }, 100) // Update every 100ms
  }

  // Handle mouse up event
  onMouseUp() {
    if (!this.isMouseDown) return

    this.isMouseDown = false

    // Stop intensity buildup
    if (this.intensityInterval) {
      clearInterval(this.intensityInterval)
      this.intensityInterval = null
    }

    // Stop voice synthesis
    if (this.voiceSynthesizer) {
      this.voiceSynthesizer.stop()
    }

    // Reset intensity
    this.currentIntensity = 0
  }

  // Clean up all resources
  dispose() {
    this.onMouseUp() // Clean up any active voice

    if (this.intensityInterval) {
      clearInterval(this.intensityInterval)
      this.intensityInterval = null
    }

    if (this.voiceSynthesizer) {
      this.voiceSynthesizer.dispose()
      this.voiceSynthesizer = null
    }
  }

  // Get current interaction state for visualization
  getInteractionState() {
    const voiceState = this.voiceSynthesizer?.getState() || {
      isActive: false,
      frequency: 0,
      vowel: "a",
      intensity: 0,
      activeLayerCount: 0,
    }

    return {
      isActive: this.isMouseDown,
      intensity: this.currentIntensity,
      frequency: voiceState.frequency,
      vowel: voiceState.vowel,
      activeLayerCount: voiceState.activeLayerCount,
    }
  }
}
