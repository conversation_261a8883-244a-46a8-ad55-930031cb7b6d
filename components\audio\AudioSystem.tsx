"use client"

import { useRef, useState, useEffect } from "react"
import * as Tone from "tone"
import { FREQUENCIES, CHORD_PROGRESSIONS } from "../../constants/frequencies"
import { SpatialLayer, BrownianLayer, AudioLevels, FrequencyData } from "../../constants/types"
import { InteractiveAudioHandler } from "./InteractiveAudioHandler"

interface AudioSystemProps {
  mode: string
  isLayered: boolean
  isBrownianActive: boolean
  isPianoActive: boolean
  pianoProgression: string
  spatialLayers: SpatialLayer[]
  brownianLayer: BrownianLayer
  onUpdateActivePositions: (positions: Set<string>) => void
  onUpdateAudioLevels: (levels: AudioLevels) => void
  interactiveHandler: InteractiveAudioHandler | null
}

export function AudioSystem({
  mode,
  isLayered,
  isBrownianActive,
  isPianoActive,
  pianoProgression,
  spatialLayers,
  brownianLayer,
  onUpdateActivePositions,
  onUpdateAudioLevels,
  interactiveHandler,
}: AudioSystemProps) {
  const oscillatorsRef = useRef<Tone.Oscillator[]>([])
  const spatialPannersRef = useRef<Tone.Panner3D[]>([])
  const brownianNoiseRef = useRef<Tone.Noise | null>(null)
  const brownianPannerRef = useRef<Tone.Panner3D | null>(null)
  const brownianVolumeRef = useRef<Tone.Volume | null>(null)
  const currentIndexRef = useRef(0)
  const [isInitialized, setIsInitialized] = useState(false)

  // Master audio chain refs
  const masterLimiterRef = useRef<Tone.Limiter | null>(null)
  const masterVolumeRef = useRef<Tone.Volume | null>(null)
  const layerVolumeRefs = useRef<(Tone.Volume | null)[]>([null, null, null])

  // Piano-related refs
  const pianoRef = useRef<Tone.PolySynth | null>(null)
  const pianoReverbRef = useRef<Tone.Reverb | null>(null)
  const pianoChorusRef = useRef<Tone.Chorus | null>(null)
  const pianoVolumeRef = useRef<Tone.Volume | null>(null)
  const pianoTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  const frequencies = FREQUENCIES[mode as keyof typeof FREQUENCIES] || FREQUENCIES.normal

  // Update interactive handler with current audio context and master volume
  useEffect(() => {
    if (interactiveHandler && masterVolumeRef.current) {
      const currentFreqs = frequencies.map((f) => f.freq)
      interactiveHandler.updateAudioContext(currentFreqs, mode, isPianoActive, pianoProgression)

      // Update the handler's master volume reference
      if ((interactiveHandler as any).masterVolume !== masterVolumeRef.current) {
        ;(interactiveHandler as any).masterVolume = masterVolumeRef.current
        // Re-initialize voice synthesizer with new master volume
        if ((interactiveHandler as any).initializeVoiceSynthesizer) {
          ;(interactiveHandler as any).initializeVoiceSynthesizer()
        }
      }
    }
  }, [interactiveHandler, frequencies, mode, isPianoActive, pianoProgression, isInitialized])

  // Update audio levels periodically
  useEffect(() => {
    const updateLevels = () => {
      if (!isInitialized) return

      // Calculate theoretical dB levels based on volume percentages and activity
      const layer1Level = spatialLayers[0] ? -30 + (spatialLayers[0].volume / 100) * 25 : -60
      const layer2Level = spatialLayers[1] ? -30 + (spatialLayers[1].volume / 100) * 25 : -60
      const layer3Level = spatialLayers[2] ? -30 + (spatialLayers[2].volume / 100) * 25 : -60
      const brownianLevel = isBrownianActive ? -30 + (brownianLayer.volume / 100) * 25 : -60

      // Master level is combination of active layers
      const activeLayers = [
        isLayered ? layer1Level : -60,
        isLayered ? layer2Level : -60,
        isLayered ? layer3Level : -60,
        brownianLevel,
      ].filter((level) => level > -60)

      const masterLevel = activeLayers.length > 0 ? Math.max(-60, -12 + 10 * Math.log10(activeLayers.length)) : -60

      onUpdateAudioLevels({
        layer1: isLayered ? layer1Level : -60,
        layer2: isLayered ? layer2Level : -60,
        layer3: layer3Level,
        brownian: brownianLevel,
        master: masterLevel,
      })
    }

    const interval = setInterval(updateLevels, 100) // Update 10 times per second
    return () => clearInterval(interval)
  }, [isInitialized, isLayered, isBrownianActive, spatialLayers, brownianLayer, onUpdateAudioLevels])

  // Initialize Tone.js
  useEffect(() => {
    const initAudio = async () => {
      if (Tone.context.state !== "running") {
        await Tone.start()
      }

      // Create master limiter and volume control to prevent clipping
      if (!masterLimiterRef.current) {
        const masterVolume = new Tone.Volume(-3) // Increased from -12dB to -3dB for audible levels
        const masterLimiter = new Tone.Limiter(-1) // Limit at -1dB to prevent clipping

        masterVolume.chain(masterLimiter, Tone.Destination)

        masterVolumeRef.current = masterVolume
        masterLimiterRef.current = masterLimiter
      }

      // Create piano with warm, gentle sound and effects
      if (!pianoRef.current) {
        const piano = new Tone.PolySynth(Tone.Synth, {
          oscillator: {
            type: "sine",
          },
          envelope: {
            attack: 0.3,
            decay: 0.4,
            sustain: 0.6,
            release: 2.0,
          },
          volume: -25,
        })

        const pianoVolume = new Tone.Volume(-15)
        const pianoReverb = new Tone.Reverb({
          roomSize: 0.8,
          dampening: 1000,
          wet: 0.4,
        })
        const pianoChorus = new Tone.Chorus({
          frequency: 0.5,
          delayTime: 3.5,
          depth: 0.3,
          wet: 0.2,
        })

        // Chain piano through effects to master
        piano.chain(pianoVolume, pianoChorus, pianoReverb, masterVolumeRef.current)

        pianoRef.current = piano
        pianoVolumeRef.current = pianoVolume
        pianoReverbRef.current = pianoReverb
        pianoChorusRef.current = pianoChorus

        // Start the chorus effect
        pianoChorus.start()
      }

      setIsInitialized(true)
    }

    initAudio()

    return () => {
      // Clean up all oscillators
      oscillatorsRef.current.forEach((osc) => {
        if (osc.state === "started") {
          osc.stop()
        }
        osc.dispose()
      })
      spatialPannersRef.current.forEach((panner) => panner.dispose())
      spatialPannersRef.current = []

      if (brownianNoiseRef.current) {
        brownianNoiseRef.current.stop()
        brownianNoiseRef.current.dispose()
        brownianNoiseRef.current = null
      }
      if (brownianPannerRef.current) {
        brownianPannerRef.current.dispose()
        brownianPannerRef.current = null
      }
      if (brownianVolumeRef.current) {
        brownianVolumeRef.current.dispose()
        brownianVolumeRef.current = null
      }

      // Clean up master effects
      if (masterLimiterRef.current) {
        masterLimiterRef.current.dispose()
        masterLimiterRef.current = null
      }
      if (masterVolumeRef.current) {
        masterVolumeRef.current.dispose()
        masterVolumeRef.current = null
      }

      // Clean up piano
      if (pianoTimeoutRef.current) {
        clearTimeout(pianoTimeoutRef.current)
      }
      if (pianoRef.current) {
        pianoRef.current.dispose()
        pianoRef.current = null
      }
      if (pianoReverbRef.current) {
        pianoReverbRef.current.dispose()
        pianoReverbRef.current = null
      }
      if (pianoChorusRef.current) {
        pianoChorusRef.current.dispose()
        pianoChorusRef.current = null
      }
      if (pianoVolumeRef.current) {
        pianoVolumeRef.current.dispose()
        pianoVolumeRef.current = null
      }
    }
  }, [])

  // Main audio playback system
  useEffect(() => {
    if (!isInitialized || mode === "normal" || !masterVolumeRef.current) {
      // Stop all current oscillators with gentle fade
      oscillatorsRef.current.forEach((osc) => {
        if (osc.state === "started") {
          osc.volume.rampTo(-60, 3)
          setTimeout(() => {
            osc.stop()
            osc.dispose()
          }, 3000)
        }
      })
      oscillatorsRef.current = []
      spatialPannersRef.current.forEach((panner) => panner.dispose())
      spatialPannersRef.current = []
      onUpdateActivePositions(new Set())
      return
    }

    const playFrequencySequence = () => {
      const currentFreq = frequencies[currentIndexRef.current]
      const activePositions = new Set<string>()

      if (isLayered) {
        // Calculate total number of oscillators for proper volume distribution
        const totalOscillators = spatialLayers.reduce((sum, layer) => sum + layer.positions.length, 0)

        // Create spatial oscillators for all layers
        spatialLayers.forEach((layer, layerIndex) => {
          layer.positions.forEach((position, posIndex) => {
            const oscillator = new Tone.Oscillator({
              frequency: currentFreq.freq + posIndex * 1.2,
              type: "sine",
              volume: -30, // Increased from -50dB to -30dB for audible levels
            })

            const panner = new Tone.Panner3D({
              positionX: position.x,
              positionY: position.y,
              positionZ: position.z,
              orientationX: 0,
              orientationY: 0,
              orientationZ: -1,
            })

            const volume = new Tone.Volume(-30)

            // Connect through master volume/limiter chain
            oscillator.chain(volume, panner, masterVolumeRef.current!)

            // Calculate audible volume level based on layer percentage
            const baseVolume = -20 // Much higher base volume for audibility
            const layerVolumeMultiplier = layer.volume / 100
            const oscillatorReduction = Math.log10(totalOscillators) * 6 // Reduced reduction
            const finalVolume = baseVolume + layerVolumeMultiplier * 20 - oscillatorReduction

            setTimeout(() => {
              oscillator.start()
              volume.volume.rampTo(Math.max(-40, finalVolume), 5) // Higher minimum volume
              activePositions.add(`${layer.id}-${posIndex}`)
            }, posIndex * 200)

            oscillatorsRef.current.push(oscillator)
            spatialPannersRef.current.push(panner)

            setTimeout(
              () => {
                if (oscillator.state === "started") {
                  volume.volume.rampTo(-60, 5)
                  setTimeout(() => {
                    oscillator.stop()
                    oscillator.dispose()
                    panner.dispose()
                  }, 5000)
                }
              },
              (currentFreq.duration - 5) * 1000,
            )
          })
        })
      } else {
        // Single stereo oscillator
        const oscillator = new Tone.Oscillator({
          frequency: currentFreq.freq,
          type: "sine",
          volume: -20, // Increased from -35dB
        })

        const volume = new Tone.Volume(-20)
        oscillator.chain(volume, masterVolumeRef.current!)

        oscillator.start()
        volume.volume.rampTo(-10, 5) // Much higher volume for audibility

        oscillatorsRef.current.push(oscillator)

        setTimeout(
          () => {
            if (oscillator.state === "started") {
              volume.volume.rampTo(-60, 5)
              setTimeout(() => {
                oscillator.stop()
                oscillator.dispose()
              }, 5000)
            }
          },
          (currentFreq.duration - 5) * 1000,
        )
      }

      onUpdateActivePositions(activePositions)

      setTimeout(() => {
        currentIndexRef.current = (currentIndexRef.current + 1) % frequencies.length
        oscillatorsRef.current = oscillatorsRef.current.filter((osc) => osc.state === "started")
        spatialPannersRef.current = spatialPannersRef.current.filter((panner) => !panner.disposed)
        playFrequencySequence()
      }, currentFreq.duration * 1000)
    }

    playFrequencySequence()
  }, [mode, isLayered, isInitialized, frequencies, spatialLayers])

  // Brownian noise system
  useEffect(() => {
    if (!isInitialized || !masterVolumeRef.current) return

    if (isBrownianActive && !brownianNoiseRef.current) {
      const brownianNoise = new Tone.Noise({
        type: "brown",
        volume: -25, // Increased from -50dB to -25dB for audibility
      })

      const volume = new Tone.Volume(-25)
      const panner = new Tone.Panner3D({
        positionX: 0,
        positionY: 0,
        positionZ: 2,
        orientationX: 0,
        orientationY: 0,
        orientationZ: -1,
      })

      // Connect through master volume/limiter chain
      brownianNoise.chain(volume, panner, masterVolumeRef.current)

      brownianNoiseRef.current = brownianNoise
      brownianVolumeRef.current = volume
      brownianPannerRef.current = panner

      brownianNoise.start()

      // Calculate volume based on brownian layer percentage - much more audible
      const brownianVolumeDb = -25 + (brownianLayer.volume / 100) * 30 // Increased range
      volume.volume.rampTo(brownianVolumeDb, 3)

      const animateSpatialMovement = () => {
        if (brownianPannerRef.current) {
          brownianPannerRef.current.positionZ.rampTo(-3, 20)
          setTimeout(() => {
            if (brownianPannerRef.current) {
              brownianPannerRef.current.positionZ.rampTo(2, 20)
              setTimeout(animateSpatialMovement, 20000)
            }
          }, 20000)
        }
      }

      animateSpatialMovement()
    } else if (!isBrownianActive && brownianNoiseRef.current) {
      if (brownianVolumeRef.current) {
        brownianVolumeRef.current.volume.rampTo(-60, 3)
      }

      setTimeout(() => {
        if (brownianNoiseRef.current) {
          brownianNoiseRef.current.stop()
          brownianNoiseRef.current.dispose()
          brownianNoiseRef.current = null
        }
        if (brownianPannerRef.current) {
          brownianPannerRef.current.dispose()
          brownianPannerRef.current = null
        }
        if (brownianVolumeRef.current) {
          brownianVolumeRef.current.dispose()
          brownianVolumeRef.current = null
        }
      }, 3000)
    }
  }, [isBrownianActive, isInitialized])

  // Update brownian noise volume when brownianLayer.volume changes
  useEffect(() => {
    if (brownianVolumeRef.current && brownianNoiseRef.current && isBrownianActive) {
      const brownianVolumeDb = -25 + (brownianLayer.volume / 100) * 30 // Increased range for audibility
      brownianVolumeRef.current.volume.rampTo(brownianVolumeDb, 1) // Quick volume adjustment
    }
  }, [brownianLayer.volume, isBrownianActive])

  // Piano chord progression system
  useEffect(() => {
    if (!isInitialized || !isPianoActive || !pianoRef.current || !masterVolumeRef.current) {
      // Stop piano if inactive
      if (pianoTimeoutRef.current) {
        clearTimeout(pianoTimeoutRef.current)
        pianoTimeoutRef.current = null
      }
      return
    }

    const progression = CHORD_PROGRESSIONS[pianoProgression as keyof typeof CHORD_PROGRESSIONS]
    if (!progression) return

    const playChordProgression = () => {
      const piano = pianoRef.current!
      const pianoVolume = pianoVolumeRef.current!

      let currentChord = 0
      const beatDuration = (60 / progression.bpm) * 1000 // Convert BPM to milliseconds per beat

      const playNextChord = () => {
        if (!isPianoActive || !pianoRef.current) return

        const chord = progression.chords[currentChord]

        if (!chord.pause && chord.notes.length > 0) {
          // Gentle volume fade in
          pianoVolume.volume.rampTo(-8, 0.5)

          // Play chord with gentle attack
          piano.triggerAttackRelease(chord.notes, chord.duration * (60 / progression.bpm))

          // Gentle fade out
          setTimeout(
            () => {
              if (pianoVolumeRef.current) {
                pianoVolume.volume.rampTo(-20, 1)
              }
            },
            chord.duration * (60 / progression.bpm) * 1000 - 1000,
          )
        }

        currentChord = (currentChord + 1) % progression.chords.length

        pianoTimeoutRef.current = setTimeout(playNextChord, chord.duration * beatDuration)
      }

      playNextChord()
    }

    playChordProgression()

    return () => {
      if (pianoTimeoutRef.current) {
        clearTimeout(pianoTimeoutRef.current)
        pianoTimeoutRef.current = null
      }
    }
  }, [isPianoActive, pianoProgression, isInitialized])

  return null
}
